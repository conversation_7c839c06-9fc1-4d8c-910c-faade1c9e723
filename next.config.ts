import type { NextConfig } from "next";

const nextConfig: NextConfig = {
    /* config options here */
    allowedDevOrigins: ['f1ff-113-160-0-10.ngrok-free.app', '*.f1ff-113-160-0-10.ngrok-free.app'],
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'platform-lookaside.fbsbx.com',
            },
            {
                protocol: 'https',
                hostname: 'lh3.googleusercontent.com',
            },
        ]
    },
    assetPrefix: '/toplist',
};

export default nextConfig;
