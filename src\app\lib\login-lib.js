function syncApiLogin(userId, userToken) {

    //window.location.reload();

    $.ajax({
        url: apiDomain + "/login.htm",
        data: {
            userId: userId,
            userToken: userToken,
        },
        crossDomain: true,
        dataType: 'jsonp',
        type: "GET",
        beforeSend: function () {

        },
        success: function (o) {
            //console.log('syncApiLogin', o);
            window.location.reload(); // Reload the page to reflect the login state
        }
    });
}

function showProfile(profile) {
    if (!isValidProfile(profile)) {
        //console.error('Invalid profile data');
        return;
    }
    //console.log('Showing profile:', profile);

    //document.getElementById('login-section').style.display = 'none';
    //document.getElementById('profile-section').style.display = 'block';
    //document.getElementById('profile-picture').src = profile.picture;
    //document.getElementById('user-name').textContent = profile.name;
    //document.getElementById('user-email').textContent = profile.email;
}

// Function to validate profile data
function isValidProfile(profile) {
    return profile &&
        typeof profile === 'object' &&
        profile.name &&
        profile.email &&
        profile.picture;
}

async function checkLogin(from) {

    let userToken = $.cookie('userToken');
    let userId = $.cookie('userId');
    var obj = await callCheckLogin(); // Call the API to check login status
    //console.log('checkLogin from: ' + from, obj);

    if (obj.status) {
        if (obj.data.id > 0) {
            // hidden login section
            $('#btn-open-popup-login').addClass('hid'); // add class hid to hide the login button
            $('#btn-logout').removeClass('hid'); // remove class hid to show the logout button
            $('#fan-login-wrapt').removeClass('hid'); // remove class hid to show khoi Fan bottom bar
            $('#fan-login-mission').removeClass('hid'); // remove class hid to show ljpo Gan bottom right mission button
            $('#popup-login-wrapt').remove(); // Xoa luon popup login

            // Luu ca object xuong localStorage
            localStorage.setItem('fan', JSON.stringify(obj.data)); // Save fan data to localStorage
            //console.log('luu store fan: ', obj.data);
            //console.log('luu store ok, ket qua lay ra: ', getFanLogin());

            // Bind bottom bar and mission button click events
            initBottomBar(obj.data);

            initAfterLogin(obj.data); // Call function to initialize after login
        } else {
            initNotLogin(); // init not login
        }
    } else {
        initNotLogin(); // init not login
    }

    if (typeof loginCallback === 'function') {
        loginCallback();
    }

    // Call lấy so vé quay và số người checkin chào cờ
    FandomYeuNuocInfo.init();
}
function initAfterLogin(fan) {
    console.log('initAfterLogin', fan);
    let $wrapt = $('#fan-login-wrapt');
    let $name = $wrapt.find('.info-user .user-link .user-name');
    let $ava = $wrapt.find('.info-user .user-link .user-ava img');

    $wrapt.attr('fanId', fan.id);
    $ava.attr('src', getThumbImage_W(fan.avatarUrl, 400) || '/Static/img/avatar.png'); // default avatar if not set
    $name.text(fan.username || ''); // default name if not set

    // Initialize after login actions here
    // For example, you can update the UI or fetch additional data
    // You can also call other functions to handle post-login tasks

    //LogActionOnline.initOnlineDaily(1, function () {
    // ✅ Đã online đủ 5 phút trong ngày
    // 👉 Gọi API log, hoặc hiện quà tặng, v.v.
    //console.log('🚀 Callback: Gửi log hoặc xử lý sự kiện tại đây');
    //});



}

function initBottomBar(fan) {
    //let $wrapt = $('#fan-login-wrapt');
    //let $ava = $wrapt.find('.left .ava-thumb img');
    //let $level = $wrapt.find('.left .level');
    //let $name = $wrapt.find('.left .name');

    //let $exp = $wrapt.find('.right .txtExp');
    //let $star = $wrapt.find('.right .txtStar');

    ////console.log('initBottomBar', fan);

    //$wrapt.attr('fanId', fan.id);
    //$ava.attr('src', getThumbImage_W(fan.avatarUrl, 400) || '/Static/img/avatar.png'); // default avatar if not set
    //$level.addClass('hid'); // $level.text(fan.level || 0); // default level if not set
    //$name.text(fan.username || ''); // default name if not set
    //$exp.addClass('hid'); //$exp.text(fan.exp || 0); // default exp if not set
    //$star.text(fan.totalPoints || 0); // default star if not set

    //if (fan.totalUncompletedChallenges > 0) {
    //    $('#fan-login-mission').removeClass('hid');
    //    $('#fan-login-mission').find('.btn-missionAction .numb').html(fan.totalUncompletedChallenges);
    //} else {
    //    $('#fan-login-mission').off('click');
    //    $('#fan-login-mission').find('.btn-missionAction .numb').remove();
    //}


}

function initNotLogin() {
    $('#btn-open-popup-login').removeClass('hid'); // remove class hid to hide the login button
    $('#btn-logout').addClass('hid'); // add class hid to show the logout button
    $('#fan-login-wrapt').addClass('hid'); // add class hid to show khoi Fan bottom bar

    // init LoginGoogle
    //LoginGoogle.init();

    // xoa thong tin fan trong localStorage
    localStorage.removeItem('fan'); // Remove fan data from localStorage

}

async function callCheckLogin() {
    try {
        const response = await fetch(apiServerAdmClient + '/auth/check-login', {
            method: 'GET',
            credentials: 'include' // Quan trọng: phải có dòng này
        });
        const data = await response.json();
        return data; // Trả về dữ liệu từ API
        //document.getElementById('result').innerHTML = JSON.stringify(data, null, 2);
    } catch (error) {
        return 'Error: ' + error.message;
    }
}

function getFanLogin() {
    const temp = localStorage.getItem('fan');
    if (!temp) return null;
    try {
        const fan = JSON.parse(temp);
        // Nếu không phải object hoặc không có id => coi như không hợp lệ
        if (!fan || typeof fan !== 'object' || !fan.id) return null;
        return fan;
    } catch (error) {
        return null;
    }
}

function formatClickActionNotLogin($btn) {
    $btn.off('click').on('click', function (e) {
        e.preventDefault();
        openPopupLogin();
    });
}

async function logOut() {
    const action = '/auth/logout';
    const res = await callApiPost(action, '');
    console.log('🔒 Đã logout:', res);
    window.location.reload();
    return res;
}

function checkUserInFandom(fandomId) {
    const user = getFanLogin(); // Gọi hàm lấy thông tin người dùng

    if (!user) {
        return -1; // Người dùng chưa đăng nhập
    }

    // Kiểm tra mảng fandomIds có tồn tại và là mảng
    if (Array.isArray(user.fandomIds)) {
        return user.fandomIds.includes(fandomId) ? 1 : 0;
    }

    return 0; // Mặc định nếu không có fandomIds hợp lệ
}

// Gọi hàm này để cập nhật fandomIds trong localStorage khi người dùng tham gia hoặc rời fandom
function updateFandomIds(fandomId, type) {
    // Lấy object user từ localStorage
    const fanData = getFanLogin(); // hàm của bạn đã có sẵn
    if (!fanData || !Array.isArray(fanData.fandomIds)) return;

    // Sao chép để tránh chỉnh trực tiếp nếu cần
    const fandomIds = [...fanData.fandomIds];

    fandomId = parseInt(fandomId, 10); // Đảm bảo là số nguyên
    if (isNaN(fandomId)) return;

    if (type === 0) {
        // Gỡ fandomId nếu tồn tại
        const index = fandomIds.indexOf(fandomId);
        if (index !== -1) {
            fandomIds.splice(index, 1);
        }
    } else if (type === 1) {
        // Thêm fandomId nếu chưa có
        if (!fandomIds.includes(fandomId)) {
            fandomIds.push(fandomId);
        }
    }

    // Cập nhật lại object và lưu vào localStorage
    fanData.fandomIds = fandomIds;
    localStorage.setItem('fan', JSON.stringify(fanData));
}
