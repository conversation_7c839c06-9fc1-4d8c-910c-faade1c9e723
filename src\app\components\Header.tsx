'use client'
import {useSession, signOut, signIn} from "next-auth/react";
import Script from "next/script";
import GoogleSignIn from "@/app/components/GoogleSignIn";
import React, { useState } from 'react';
declare global {
    function fbLogin(): void;
}

const Header = () => {
    const { data: session } = useSession();
    const appId = "1243027207556797";
    const clientId = "911881365664-ojki7o7iforlgi4u4albakj6lrpu6b2s.apps.googleusercontent.com";

    const [searchValue, setSearchValue] = useState('');

    const handleFacebookLogin = () => {
        if (typeof window !== 'undefined' && window.fbLogin) {
            window.fbLogin();
        } else {
            console.error('fbLogin function not available');
        }
    };

    const handleSearch = () => {
        if (searchValue.trim() !== '') {
            const url = `http://localhost:3000/search?key=${encodeURIComponent(searchValue)}`;
            window.open(url, '_blank');
        }
    };


    return (
        <>
            <div id="fb-root"></div>
            {appId && (
                <Script
                    strategy="afterInteractive"
                    crossOrigin="anonymous"
                    src={`https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.3&appId=${appId}&autoLogAppEvents=1`}
                />
            )}

            <header className="header header-headroom" id="header" data-options='{"offset":0,"tolerance":5}'>
                <div className="container container-lg-fluid d-flex gap-2">
                    <button className="btn btn-primary header-btn" type="button">
                        <svg className="iconsvg-arrow-left icon-lg flex-shrink-0">
                            <use xlinkHref={"/images/sprite.svg#arrow-left"}></use>
                        </svg>
                        <span className="d-none d-lg-inline">Back</span>
                    </button>
                    <div className="header-actions flex-center-y gap-2 ms-auto">
                        <div className="input-group" style={{ display: 'none' }}>
                            <input
                                className="form-control form-control-icon-start header-search"
                                type="text"
                                aria-label="Tìm kiếm"
                                placeholder="Tìm merchandise..."
                                value={searchValue}
                                onChange={e => setSearchValue(e.target.value)}
                            />
                            <button className="btn " type="button"
                                    style={{
                                        backgroundColor: 'white', // hoặc dùng getComputedStyle nếu dynamic
                                        borderLeft: 'none',
                                        borderRadius: '0 50% 50% 0', // bo góc bên phải
                                        padding: '0.375rem 0.75rem',
                                    }}
                                    onClick={handleSearch}>
                                🔍
                            </button>
                        </div>
                        <button className="btn btn-primary header-btn text-nowrap" type="button" onClick={handleSearch}>
                            <svg className="iconsvg-gift icon-lg flex-shrink-0">
                                <use xlinkHref={"/images/sprite.svg#gift"}></use>
                            </svg>
                            <span className="d-none d-lg-inline">Chơi game nhận quà</span>
                        </button>
                        <div className="header-dropdown-acc dropdown">
                            <button
                                className="btn btn-primary header-btn text-nowrap text-uppercase ff-secondary fw-bold dropdown-toggle"
                                type="button"
                                data-bs-toggle="dropdown"
                                data-bs-offset="0, 8"
                                aria-expanded="false"
                            >
                                <span className="avatar avatar-xs">
                                    <img
                                        className="img-fluid w-100 avatar-img"
                                        src={session?.user?.avatar ||  '/images/ex/avatar.webp'}
                                        alt=""
                                    />
                                </span>
                                <span className="d-none d-lg-inline">
                                    {session?.user?.name || 'Đăng nhập'}
                                </span>
                            </button>
                            <ul className="dropdown-menu dropdown-menu-end">
                                {session ? (
                                    <>
                                        <li><span className="dropdown-item-text">Tên: {session.user?.name}</span></li>
                                        <li><span className="dropdown-item-text">Email: {session.user?.email}</span>
                                        </li>
                                        <li>
                                            <hr className="dropdown-divider"/>
                                        </li>
                                        <li><a className="dropdown-item" href="#">Profile</a></li>
                                        <li><a className="dropdown-item" href="#">Settings</a></li>
                                        <li><a className="dropdown-item" onClick={() => signOut()}>Logout</a></li>
                                    </>
                                ) : (
                                    <>
                                        <div className="auth-buttons-container">
                                            <li>
                                                <button
                                                    onClick={handleFacebookLogin}
                                                    className="facebook-button"
                                                >
                                                    <img
                                                        src="https://upload.wikimedia.org/wikipedia/commons/0/05/Facebook_Logo_%282019%29.png"
                                                        alt="Facebook"
                                                        className="facebook-logo"
                                                    />
                                                    Đăng nhập bằng Facebook
                                                </button>
                                            </li>
                                            <li>
                                                <div className="dropdown-item auth-button-margin">
                                                    <GoogleSignIn clientId={clientId}/>
                                                </div>
                                            </li>
                                        </div>
                                    </>
                                )}
                            </ul>
                        </div>
                    </div>
                </div>
            </header>
        </>
    );
};

export default Header;
