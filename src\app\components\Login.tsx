'use client'
import {signIn, useSession} from "next-auth/react";
import {useEffect} from "react";
import {useRouter} from "next/navigation";
const Login = () => {
    const {status} = useSession();
    const router = useRouter();
    const loginWithToken = async (token: string) => {
        // Sử dụng phone_number làm identifier và id làm password
        console.log('token', token);
        const result = await signIn("credentials", {
            redirect: false,
            acc_fandom: token,
        });
        if (result.ok && result.status === 200) {
            console.log("Logged in successfully");
        }else {
            console.log("Login failed");
        }
    }

    useEffect(() => {
        let token: string | null = null;
        if (typeof document !== 'undefined') {
            const match = document.cookie.match(/(?:^|; )sessionToken=([^;]*)/);
            if (match && match[1]) {
                token = decodeURIComponent(match[1]);
            }
        }
        console.log('status', status);
        if (token && status !== 'authenticated') {
            console.log('Token found in cookie:', token);
            loginWithToken(token);
        } else {
            console.log('Token not found in cookie');
        }
    }, [status]);

    useEffect(() => {
        const handleMessage = async (event: MessageEvent) => {
            if (typeof event.data === "object" && event.data.accessToken) {
                try {
                    const data = event.data;
                    loginWithToken(data.accessToken);
                    console.log('Login with token:', data.accessToken);
                } catch (err) {
                    console.warn('event.data is not valid JSON:', err);
                }
            }

        }
        // Lắng nghe trên cả window và document để hỗ trợ cả iOS và Android
        window.addEventListener('message', handleMessage);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (document as any).addEventListener('message', handleMessage);

        return () => {
            window.removeEventListener('message', handleMessage);
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (document as any).removeEventListener('message', handleMessage);
        };
    }, [router]);
    return (
        <div>
        </div>
    )
}

export default Login;
