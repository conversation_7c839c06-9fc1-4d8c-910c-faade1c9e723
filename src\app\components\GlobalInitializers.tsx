// app/components/GlobalInitializers.tsx
"use client";

import { useEffect, useCallback } from "react";
import type { HeadroomOptions, HeadroomInstance, SwiperOptions, SwiperInstance, VanillaOTPInstance } from '../global';

// Helper function để merge object, không thay đổi
const extend = <T extends Record<string, unknown>>(...args: Array<Partial<T> | null | undefined>): T => {
  const out: Partial<T> = args[0] ? { ...args[0] } : {};
  for (let i = 1; i < args.length; i++) {
    const source = args[i];
    if (source) {
      for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          out[key] = source[key];
        }
      }
    }
  }
  return out as T;
};

/**
 * HÀM HELPER TỐI ƯU: Chờ một thư viện global sẵn sàng.
 * Hàm này sẽ kiểm tra sự tồn tại của một thuộc tính trên đối tượng window.
 * <PERSON><PERSON><PERSON> chưa có, nó sẽ đợi một chút rồi thử lại.
 * @param libraryName Tên của thư viện trên `window` (ví dụ: 'Swiper', 'Headroom', 'bootstrap').
 * @returns Promise sẽ resolve khi thư viện đã sẵn sàng.
 */
const waitForLibrary = (libraryName: keyof Window): Promise<true> => {
  return new Promise((resolve) => {
    const check = () => {
      // Dùng `in` để kiểm tra an toàn cho cả các thuộc tính lồng nhau như 'bootstrap.Modal'
      // Tuy nhiên, ở đây ta chỉ cần kiểm tra cấp đầu tiên.
      if (typeof window !== "undefined" && window[libraryName]) {
        resolve(true);
      } else {
        setTimeout(check, 100); // Thử lại sau 100ms
      }
    };
    check();
  });
};


export const GlobalInitializers = () => {
  // --- Headroom Initialization ---
  const initializeHeadroom = useCallback(async (root: Document | HTMLElement = document) => {
    await waitForLibrary('Headroom'); // Chờ Headroom sẵn sàng

    const elHeaderHeadroom = root.querySelector<HTMLElement>('#header:not([data-headroom-initialized])');
    if (elHeaderHeadroom) {
      console.log('initializeHeadroom', elHeaderHeadroom);
      try {
        const defaults: Partial<HeadroomOptions> = {};
        const dataOptionsRaw = elHeaderHeadroom.getAttribute('data-options');
        const parsedOptions = dataOptionsRaw ? JSON.parse(dataOptionsRaw) as Partial<HeadroomOptions> : {};
        const options: Partial<HeadroomOptions> = extend({}, defaults, parsedOptions);

        const headroom: HeadroomInstance = new window.Headroom!(elHeaderHeadroom, options);
        headroom.init();
        elHeaderHeadroom.setAttribute('data-headroom-initialized', 'true');
      } catch (e) {
        console.error("Error initializing Headroom:", e);
      }
    }
  }, []);

  // --- Swiper Initialization ---
  // const initializeSwiper = useCallback(async (root: Document | HTMLElement = document) => {
  //   await waitForLibrary('Swiper'); // Chờ Swiper sẵn sàng

  //   const elSwiperAll = root.querySelectorAll<HTMLElement & { swiper?: SwiperInstance }>(
  //     '[data-plugin="swiper"]:not([data-swiper-initialized])'
  //   );

  //   if (!elSwiperAll.length) return;
  //   console.log('initializeSwiper', elSwiperAll);
  //   elSwiperAll.forEach((el) => {
  //     try {
  //       if (!el.isConnected) return;
  //       if (el.swiper) el.swiper.destroy(true, true);

  //       const nextButton = el.querySelector<HTMLElement>('.swiper-button-next');
  //       const prevButton = el.querySelector<HTMLElement>('.swiper-button-prev');
  //       const pagination = el.querySelector<HTMLElement>('.swiper-pagination');

  //       const defaults: Partial<SwiperOptions> = {
  //         navigation: { nextEl: nextButton, prevEl: prevButton },
  //         pagination: pagination ? { el: pagination, clickable: true } : undefined,
  //       };

  //       const dataOptionsRaw = el.getAttribute('data-options');
  //       const parsedOptions = dataOptionsRaw ? JSON.parse(dataOptionsRaw) as Partial<SwiperOptions> : {};

  //       const options: Partial<SwiperOptions> = extend({}, defaults, parsedOptions);
  //       const swiperInstance = new window.Swiper!(el, options);

  //       el.swiper = swiperInstance;
  //       el.setAttribute('data-swiper-initialized', 'true');
  //     } catch (e) {
  //       console.error("Error initializing Swiper for element:", el, e);
  //     }
  //   });
  // }, []);

  // --- Bootstrap Modal Adjustments ---
  const initializeBootstrapModals = useCallback(async (root: Document | HTMLElement = document) => {
    await waitForLibrary('bootstrap'); // Chờ Bootstrap sẵn sàng

    const modals = root.querySelectorAll<HTMLElement>('.modal:not([data-bs-modal-listeners-added])');

    const header = document.querySelector<HTMLElement>('#header');
    if (!header || !modals.length) return;
    console.log('initializeBootstrapModals', modals)
    const scrollbarWidth = window.innerWidth - document.body.clientWidth;

    modals.forEach((el) => {
      el.addEventListener('show.bs.modal', () => {
        header.style.right = `${scrollbarWidth}px`;
      });
      el.addEventListener('hidden.bs.modal', () => {
        header.style.right = '0';
      });
      el.setAttribute('data-bs-modal-listeners-added', 'true');
    });
  }, []);

  // --- Vanilla OTP Input Initialization ---
  const initializeVanillaOTP = useCallback(async (root: Document | HTMLElement = document) => {
    await waitForLibrary('VanillaOTP'); // Chờ VanillaOTP sẵn sàng

    const elOtpInputs = root.querySelectorAll<HTMLElement>('[data-plugin="vanilla-otp-input"]:not([data-otp-initialized])');
    if(!elOtpInputs.length) return;
    console.log('initializeVanillaOTP', elOtpInputs)
    elOtpInputs.forEach((el: HTMLElement) => {
      try {
        const otpInstance = new window.VanillaOTP!(el);
        (el as HTMLElement & { otpInstance?: VanillaOTPInstance }).otpInstance = otpInstance;
        el.setAttribute('data-otp-initialized', 'true');
      } catch (e) {
        console.error("Error initializing VanillaOTP for element:", el, e);
      }
    });
  }, []);


  // --- Main useEffect for initialization and MutationObserver ---
  useEffect(() => {
    const runInitializers = (root: Document | HTMLElement = document) => {
      // Các hàm này bây giờ là async nhưng chúng ta không cần "await" ở đây.
      // Chúng sẽ tự xử lý việc chờ đợi bên trong.
      initializeHeadroom(root);
      // initializeSwiper(root);
      initializeBootstrapModals(root);
      initializeVanillaOTP(root);
    };

    runInitializers(); // Chạy lần đầu

    // MutationObserver để khởi tạo cho các element được thêm vào DOM sau này
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Khởi tạo cho các plugin bên trong node mới được thêm vào
              runInitializers(node as HTMLElement);

              // Xử lý trường hợp đặc biệt nếu chính node đó là element cần khởi tạo
              // const elementNode = node as HTMLElement;
              // if (elementNode.matches && elementNode.matches('[data-plugin="swiper"]')) {
              //    initializeSwiper(document);
              // }
              // ... các kiểm tra khác nếu cần
            }
          });
        }
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, [initializeHeadroom,
    // initializeSwiper,
    initializeBootstrapModals,
    initializeVanillaOTP]);

  return null;
};
