'use client';

import { useState, useEffect, useCallback } from 'react';
import Swal from 'sweetalert2';
import { signIn, useSession } from 'next-auth/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/autoplay';
import commonFunc from '../utils/common'
import { API_ENDPOINTS } from '../utils/api-endpoints';

// Define the structure of a sample wish
interface SampleWish {
    id: number;
    content: string;
}

interface FeaturedWish {
    id: number;
    content: string;
    user:{
        name: string;
        avatar: string;
    },
    created_at: string;
}

interface PrizeData {
    id?: number;
    name?: string;
    type?: string;
    voucher_code?: string;
    voucher_link?: string;
    voucher_expired?: string;
    status?: string;
    image?: string;
    brand_logo?: string;
    [key: string]: unknown;
}

// --- API Header Generation ---
// This function creates the necessary headers for the sample wishes API.


const Game = () => {
    const { data: session, status } = useSession();
    const [wishCount, setWishCount] = useState<number>(0);
    const [sampleWishes, setSampleWishes] = useState<SampleWish[]>([]);
    const [featuredWishes, setFeaturedWishes] = useState<FeaturedWish[]>([]);
    const [wishInput, setWishInput] = useState<string>('');
    const [originalSampleContent, setOriginalSampleContent] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isSending, setIsSending] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [hasReceivedPrize, setHasReceivedPrize] = useState<boolean>(false);
    const [showWishForm, setShowWishForm] = useState<boolean>(false);
    const [sentWishContent, setSentWishContent] = useState<string>('');
    const [prizeData, setPrizeData] = useState<PrizeData | null>(null);
    const [totalWishCount, setTotalWishCount] = useState<number>(100000);
    const [sliderMessages, setSliderMessages] = useState<string[]>([]);

    // --- API Fetching Functions ---

    // Fetches the number of wishes the user has left.
    const fetchWishCount = useCallback(async () => {
        if (!session?.user.accessToken) return;
        try {
            const res = await fetch(`${API_ENDPOINTS.wishCount}`, {
                headers: {
                    'authorization': `Bearer ${session.user.accessToken}`,
                },
            });
            const data = await res.json();
            if (data.status) {
                setWishCount(data.data.wish_counts);
            } else {
                setError(data.message);
            }
        } catch (error) {
            console.log('fetchWishCount error', error);
            setError('Failed to fetch wish count. Please try again later.');
        }
    }, [session?.user.accessToken]);

    // Fetches the list of sample wishes.
    const fetchSampleWishes = async () => {
        try {
            console.log('fetchSampleWishes');
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.wishSample}`, { headers });
            console.log('fetchSampleWishes res', res);
            const data = await res.json();
            console.log('fetchSampleWishes  data', data);
            if (data.status) {
                setSampleWishes(data.data.wishes);
            } else {
                setError(data.message);
            }
        } catch (error) {
            console.log('fetchSampleWishes error', error);
            setError('Failed to fetch sample wishes. Please try again later.');
        }
    };

    const fetchFeaturedWishes = useCallback(async () => {
        try {
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.wishFeatured}`, { headers });
            const data = await res.json();
            if (data.status) {
                setFeaturedWishes(data.data.wishes);
            } else {
                setError(data.message);
            }
        } catch (error) {
            console.log('fetchFeaturedWishes error', error);
            setError('Failed to fetch featured wishes. Please try again later.');
        }
    }, []);

    const fetchWinners = async () => {
        try {
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.wishWinners}?limit=50&offset=0`, { headers });
            const data = await res.json();
            if (data.status && data.data.winners) {
                const messages = data.data.winners.map((winner: { userName: string; prizeName: string; }) => `${winner.userName} đã nhận được ${winner.prizeName}`);
                setSliderMessages(messages);
            } else {
                setError(data.message);
            }
            if(data.status && data.data.total_wish_users){
                setTotalWishCount(data.data.total_wish_users);
            }
        } catch (error) {
            console.log('fetchWinners error', error);
            setError('Failed to fetch winners. Please try again later.');
        }
    };

    // Sends the user's wish to the server.
    const handleSendWish = async () => {
        if (!session?.user.accessToken) {
            setError('Bạn cần đăng nhập để gửi lời chúc.');
            await Swal.fire({ icon: 'info', title: 'Cần đăng nhập', text: 'Vui lòng đăng nhập để gửi lời chúc.' });
            return;
        }
        if (!wishInput.trim()) {
            setError('Nội dung lời chúc không được để trống.');
            await Swal.fire({ icon: 'warning', title: 'Thiếu nội dung', text: 'Vui lòng nhập lời chúc trước khi gửi.' });
            return;
        }
        if (session && wishCount <= 0) {
            setError('Bạn đã hết lượt gửi lời chúc.');
            await Swal.fire({ icon: 'info', title: 'Hết lượt', text: 'Bạn đã hết lượt gửi lời chúc hôm nay.' });
            return;
        }

        setIsSending(true);
        setError(null);
        setSuccessMessage(null);

        // Determine if the wish is a sample or a custom one.
        // If a sample was selected and then edited, it's considered custom (is_sample = 0).
        const isSample = (originalSampleContent !== null && wishInput === originalSampleContent) ? 1 : 0;

        try {
            const res = await fetch(`${API_ENDPOINTS.wishSend}`, {
                method: 'POST',
                headers: {
                    'authorization': `Bearer ${session.user.accessToken}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    wish_content: wishInput,
                    is_sample: isSample.toString(),
                }),
            });
            const data = await res.json();
            if (data.status) {
                setSuccessMessage(data.message || 'Gửi lời chúc thành công!');
                setWishCount(data.data.remaining_counts);
                setSentWishContent(wishInput); // Store the sent wish content
                setWishInput('');
                setOriginalSampleContent(null);

                // Check if user received a prize
                if (data.data.prize) {
                    console.log('User received prize:', data.data.prize);
                    setPrizeData(data.data.prize);
                    setHasReceivedPrize(true);
                    setShowWishForm(true); // Show wish form in featured section when has prize
                    console.log('Set states: hasReceivedPrize=true, showWishForm=true');
                    await Swal.fire({
                        icon: 'success',
                        title: 'Chúc mừng! Bạn nhận được quà',
                        text: data.data.prize?.name || 'Bạn vừa nhận được một phần quà',
                        confirmButtonText: 'OK'
                    });
                } else {
                    console.log('User did not receive prize');
                    setPrizeData(null);
                    setHasReceivedPrize(false);
                    setShowWishForm(true); // Show wish form in featured section when no prize
                    console.log('Set states: hasReceivedPrize=false, showWishForm=true');
                    await Swal.fire({
                        icon: 'success',
                        title: 'Gửi lời chúc thành công',
                        text: data.message || 'Cảm ơn bạn đã gửi lời chúc!',
                    });
                }
            } else {
                setError(data.message || 'An unknown error occurred.');
                await Swal.fire({ icon: 'error', title: 'Gửi thất bại', text: data.message || 'Vui lòng thử lại sau.' });
            }
        } catch (error) {
            console.log('handleSendWish error', error);
            setError('Failed to send your wish. Please check your connection.');
            await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể gửi lời chúc. Vui lòng thử lại.' });
        } finally {
            setIsSending(false);
        }
    };

    // --- Effects ---

    // Fetch initial data when the component mounts
    useEffect(() => {
        console.log('useEffect session', status);
        setIsLoading(true);
        console.log('useEffect');

        // Always fetch sample wishes and featured wishes (no auth required)
        const promises = [fetchSampleWishes(), fetchFeaturedWishes(), fetchWinners()];

        Promise.all(promises).finally(() => {
            setIsLoading(false);
        });
    }, []);

    useEffect(() => {
        if (status === 'authenticated') {
            fetchWishCount();
        }
    }, [status, fetchWishCount]);

    // --- Event Handlers ---

    const handleSampleWishClick = (content: string) => {
        setWishInput(content);
        setOriginalSampleContent(content); // Store the original content to check for edits
    };

    // --- Render ---

    if (isLoading) {
        return <div className="flex justify-center items-center h-64"><div className="loader"></div><p className="ml-4 text-gray-400">Loading Game...</p></div>;
    }

    return (
    <section className="section-cta section section-has-bg-video overflow-hidden" style={{position: 'relative'}}>
    <div className="container">
          <div className="row gy-5">
             <div className="col-md-12 col-lg-6">
                        {/* Slider */}
                        <div className="swiper-container">
                            <Swiper
                                modules={[Autoplay]}
                                slidesPerView={1.5}
                                spaceBetween={30}
                                autoplay={{
                                    delay: 2500,
                                    disableOnInteraction: false,
                                }}
                                className="mySwiper"
                            >
                                {sliderMessages.map((message, index) => (
                                    <SwiperSlide key={index}>
                                        <div className="gradient-text-gold fw-bold">{message}</div>
                                    </SwiperSlide>
                                ))}
                            </Swiper>
                        </div>

                        {/* Wish Form - Show if not sent wish yet */}
                        {!showWishForm && !hasReceivedPrize && (
                            <div className="wish-container" id="wish-sample">
                                <p className="pt-30px px-30px c-white-60 mb-2 fw-bold">Câu chúc gợi ý</p>
                                <div className="mt-2 px-30px d-flex gap-2 flex-wrap mb-2 text-white">
                                    {sampleWishes.map((wish) => (
                                        <div
                                            key={wish.id}
                                            className="wish-sample"
                                            onClick={() => handleSampleWishClick(wish.content)}
                                            style={{ cursor: 'pointer' }}
                                        >
                                            {wish.content}
                                        </div>
                                    ))}
                                </div>

                                <div className="wish-form">
                                    <img src={session?.user?.image || "/img/avatar-t.jpg"} className="wish-form-avatar" alt="" />
                                    <div className="wish-form-ta fw-bold">
                                        <textarea
                                            className="form-control gradient-text-gold2 w-100 h-100 custom-scroll"
                                            rows={3}
                                            value={wishInput}
                                            onChange={(e) => setWishInput(e.target.value)}
                                            placeholder={session ? "Nhập lời chúc của bạn..." : "Đăng nhập để gửi lời chúc..."}
                                            disabled={!session || isSending || (session && wishCount <= 0)}
                                        />
                                        <div className="gradient-text-gold2 fw-bold wish-form-name">
                                            {session?.user?.name || 'Đăng nhập để tham gia'}
                                        </div>
                                    </div>
                                </div>

                                <div className="d-flex justify-content-center pb-3">
                                    <button
                                        className="btn btn-send mt-3 text-uppercase"
                                        onClick={handleSendWish}
                                        disabled={!session || isSending || (session && wishCount <= 0) || !wishInput.trim()}
                                        title={!session ? "Vui lòng đăng nhập để gửi lời chúc" : ""}
                                    >
                                        <img src="/img/btn-send.svg" alt="" />
                                        {!session ? 'Đăng nhập để gửi' : isSending ? 'Đang gửi...' : 'Gửi lời chúc'}
                                    </button>
                                </div>

                                {/* Error and Success messages */}
                                {error && <div className="alert alert-danger mx-3">{error}</div>}
                                {successMessage && <div className="alert alert-success mx-3">{successMessage}</div>}
                            </div>
                        )}

                        {/* Prize Section - Show after sending wish and receiving prize */}
                        {hasReceivedPrize && prizeData && (
                            <div className="wish-prize">
                                <div className="wish-prize-thank">
                                    <div className="gradient-text-gold fw-bold fs-4 text-uppercase">Cám ơn bạn đã gửi lời chúc</div>
                                    <div className="fs-4 fw-400 text-white">Bạn đã nhận được phần quà</div>
                                </div>

                                <div>
                                    <img src={prizeData.image || "/img/image-prize.jpg"} alt="Prize" />
                                </div>

                                {/* Prize Details */}
                                <div className="text-center">
                                    <div className="text-uppercase gradient-text-gold fw-bold fs-4">
                                        {prizeData.name || ''}
                                    </div>

                                    {/* Brand logo for physical prizes */}
                                    {prizeData.type !== 'voucher' && prizeData.brand_logo && (
                                        <div className="text-uppercase text-center mt-3">
                                            <img src={prizeData.brand_logo} alt="Brand Logo" />
                                        </div>
                                    )}
                                </div>

                                <div className="d-flex justify-content-center pb-3">
                                    <button className="btn btn-send text-uppercase">
                                        Xem quà của bạn
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Thank you message when no prize */}
                        {showWishForm && !hasReceivedPrize && (
                            <div className="wish-container">
                                <div className="d-flex flex-column align-items-center py-4">
                                    <div className="gradient-text-gold fw-bold fs-4 text-uppercase text-center mb-3">
                                        Cám ơn bạn đã gửi lời chúc
                                    </div>
                                    <div className="text-white text-center">
                                        Lời chúc của bạn đã được gửi thành công. Hãy tiếp tục theo dõi để có cơ hội nhận quà!
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Featured Wishes Section */}
                    <div className="col-md-12 col-lg-6 position-relative">
                        <p className="gradient-text-gold lc-noi-bat-title text-uppercase">Gửi lời chúc hay nhận quà liền tay</p>
                        <div className="position-relative">
                            {featuredWishes.slice(0, showWishForm ? 1 : 3).map((wish, index) => (
                                <div key={wish.id} className={`d-flex lc-noi-bat-card gap-2 ${!showWishForm && index === 1 ? 'lc-noi-bat-card-2' : !showWishForm && index === 2 ? 'lc-noi-bat-card-3' : ''}`}>
                                    <div className="lc-noi-bat-card-left d-flex flex-column align-items-center">
                                        <div>
                                            <img src="/img/fd-yn.png" alt="" />
                                        </div>
                                        <div>
                                            <img className="lc-noi-bat-card-avatar" src={wish.user.avatar || "/img/avatar-t.jpg"} alt="" />
                                        </div>
                                        <div className="lc-noi-bat-card-name gradient-text-gold2">{wish.user.name}</div>
                                    </div>
                                    <div className="lc-noi-bat-card-right d-flex flex-column">
                                        <img className="position-absolute nhay-kep-nguoc" src="/img/nhay-kep-nguoc.svg" alt="" />
                                        <div className="gradient-text-gold2 lc-noi-bat-card-content">{wish.content}</div>
                                        <img className="position-absolute nhay-kep" src="/img/nhay-kep.svg" alt="" />
                                    </div>
                                </div>
                            ))}

                            {/* User's wish form after sending */}
                            {showWishForm && (
                                <div className="wish-form2" style={{top: '76px', right: '20px'}}>
                                    <img src={session?.user?.image || "/img/avatar-t.jpg"} className="wish-form-avatar" alt="" />
                                    <div className="wish-form-ct">
                                        <div className="gradient-text-gold2 wish-form-ct-content">{sentWishContent}</div>
                                        <div className="gradient-text-gold2 fw-bold wish-form-ct-name mt-3">{session?.user?.name || 'Người dùng'}</div>
                                    </div>
                                </div>
                            )}
                        </div>

                        <div className="position-absolute total-wish">
                            {totalWishCount.toLocaleString()}+ người đã gửi lời chúc
                        </div>
                    </div>
          </div>
        </div>
                <img className="img-fluid section-deco d-block" src="/images/home/<USER>/deco.webp" alt="" />
                <video className="section-bg-video" autoPlay muted loop playsInline>
                    <source src={"/images/home/<USER>"} type="video/mp4" />Your browser does not support the video tag.
                </video>
        <style jsx>{`
                .game-container{
                    background: url('/img/bg.png');
                    width: 100vw;
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    min-height: 780px;
                }

                .fs-14px{
                    font-size: 14px;
                }

                .mx-30px{
                    margin: 30px 0;
                }

                .px-30px{
                    padding: 0 30px;
                }

                .py-30px{
                    padding: 30px 0;
                }

                .pt-30px{
                    padding-top: 30px;
                }
                .mb-30px{
                    margin-bottom: 30px;
                }

                .gap-30px{
                    gap: 30px;
                }

                .color-gold{
                    background: -webkit-linear-gradient(#FAEEDD, #E9AF62, #F2D4AC, #FFC77D, #FDF0DF );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                .gradient-text-gold {
                    background: linear-gradient(
                        to right,
                        #FAEEDD 0%,
                        #E9AF62 25%,
                        #F2D4AC 52%,
                        #FFC77D 80%,
                        #FDF0DF 100%
                    );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    color: transparent;
                }

                .gradient-text-gold2 {
                    background: linear-gradient(
                        to right,
                        #FFFCF4 0%,
                        #FFECB2 17%,
                        #FFE9AE 33%,
                        #FFCB82 50%,
                        #FFECAE 67%,
                        #FFF7DE 83%,
                        #FFFFFF 100%
                    );
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    color: transparent;
                }

                .swiper-container{
                    background-color: #95000030;
                    padding: 0.625rem;
                    margin-bottom: 0.625rem;
                    border-radius: 0.5rem;
                }

                .wish-container{
                    background-color: #ffffff20;
                    border-radius: 24px;
                    padding: 0px 10px;
                    min-height: 659px;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                }

                .wish-prize{
                    background-color: #ffffff20;
                    border-radius: 24px;
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                    align-items: center;
                    min-height: 669px;
                }

                .c-white-60{
                    color: #ffffff60
                }

                .wish-sample{
                    border-radius: 1.5rem;
                    padding: 10px 16px;
                    border: 1px solid #FFFFFF20;
                }

                .wish-form{
                    background: url('/img/frame.png');
                    background-size: cover;
                    background-repeat: no-repeat;
                    background-position: center;
                    height: 340px;
                    margin-top: 10px;
                    position: relative;
                    width: 628px;
                    margin: auto;
                    min-width: 628px;
                }

                .wish-form2{
                    background: url('/img/frame2.png');
                    background-size: cover;
                    background-repeat: no-repeat;
                    background-position: center;
                    height: 300px;
                    position: relative;
                    width: 568px;
                    min-width: 550px;
                }

                .wish-form-ta{
                    max-width: 343px;
                    background-color: #950000;
                    position: absolute;
                    border-radius: 0.5rem;
                    border: 1px solid #FFFFFF30 !important;
                    height: 92px;
                    top: 125px;
                    right: 30px;
                    width: 100%;
                    padding: 0.375rem 0.375rem 0.375rem 0.75rem;
                }

                .wish-form-ct{
                    max-width: 310px;
                    position: absolute;
                    top: 105px;
                    width: 100%;
                    padding: 0.375rem 0.375rem 0.375rem 0.75rem;
                    right: 32px
                }

                .wish-form-ct-content{
                    font-size: 15px;
                }

                .wish-form-ct-name{
                    font-size: 12px
                }
                .wish-form-ta textarea{
                    padding: 0;
                    border: 0;
                    caret-color: #FFCB82
                }
                .wish-form-ta textarea:focus{
                    background-color: #950000;
                    box-shadow: none;
                    caret-color: #FFCB82;
                    color: transparent
                }

                .wish-form-name{
                    position: absolute;
                    bottom: -40px;
                }

                .custom-scroll {
                    scrollbar-width: thin;
                    scrollbar-color: #888 #f1f1f1;
                }

                .custom-scroll::-webkit-scrollbar {
                    width: 8px;
                }

                .custom-scroll::-webkit-scrollbar-thumb {
                    background-color: #888;
                    border-radius: 4px;
                }

                .custom-scroll::-webkit-scrollbar-track {
                    background-color: #f1f1f1;
                }

                .btn-send{
                    display: flex;
                    gap: 12px;
                    background-color: #C92027;
                    border-radius: 1rem;
                    padding: 12px 24px;
                    box-shadow: 0px 4px 0px 0px #8D0006;
                    color: #FFFFFF;
                    font-size: 17px;
                    font-weight: 600;
                    border: none;
                    cursor: pointer;
                }

                .btn-send:hover {
                    background-color: #B01B21;
                }

                .btn-send:disabled {
                    background-color: #666;
                    cursor: not-allowed;
                }

                .wish-form-avatar{
                    width: 142px;
                    height: auto;
                    aspect-ratio: 1/1;
                    border-radius: 50%;
                    position: absolute;
                    top: 10px;
                    left: 34px;
                }

                .lc-noi-bat-title{
                    font-size: 30px;
                    font-weight: bold;
                    padding-left: 55px;
                    padding-top: 17px;
                }

                .lc-noi-bat-card{
                    min-width: 435px;
                    width: 435px;
                    border-radius: 1rem;
                    background-color: #FF950050;
                    backdrop-filter: blur(50px);
                    -webkit-backdrop-filter: blur(50px);
                    box-shadow: 0px 4px 12px 0px #FF440025;
                    padding: 24px;
                }

                .lc-noi-bat-card-right{
                    position: relative;
                }

                .lc-noi-bat-card-avatar{
                    width: 60px;
                    height: auto;
                    aspect-ratio: 1/1;
                    border-radius: 50%;
                }

                .lc-noi-bat-card-name{
                    margin-top: 12px;
                    font-weight: bold;
                    font-size: 12px;
                }

                .nhay-kep-nguoc, .nhay-kep{
                    width: 20px;
                    height: auto;
                }

                .lc-noi-bat-card-content{
                    padding-top: 24px;
                    font-size: 15px;
                }

                .nhay-kep{
                    right: 0;
                    bottom: 0;
                }

                .lc-noi-bat-card-2{
                    top: -40px;
                    left: 40%;
                    position: relative;
                }

                .lc-noi-bat-card-3{
                    top: 60px;
                    position: relative;
                }

                .total-wish{
                    background: linear-gradient(
                        to right,
                        #FFFF10 0%,
                        #FF5912 50%,
                        #FFF50D 79%,
                        #FF5712 100%
                    );
                    border-radius: 16px;
                    padding: 6px 20px;
                    color: #CB0000;
                    font-size: 13px;
                    font-weight: bold;
                    bottom: 0;
                }

                .wish-prize-thank{
                    margin-top: 61px;
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                    align-items: center;
                }

                .loader {
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #3498db;
                    border-radius: 50%;
                    width: 30px;
                    height: 30px;
                    animation: spin 1s linear infinite;
                }
                .loader-sm {
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #ffffff;
                    border-radius: 50%;
                    width: 16px;
                    height: 16px;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .alert {
                    padding: 12px;
                    margin: 12px 0;
                    border-radius: 4px;
                }
                .alert-danger {
                    background-color: rgba(220, 53, 69, 0.2);
                    border: 1px solid #dc3545;
                    color: #dc3545;
                }
                .alert-success {
                    background-color: rgba(40, 167, 69, 0.2);
                    border: 1px solid #28a745;
                    color: #28a745;
                }
                .alert-info {
                    background-color: rgba(23, 162, 184, 0.2);
                    border: 1px solid #17a2b8;
                    color: #17a2b8;
                }
                    /* Style for video background */
                    .section-has-bg-video .section-bg-video {
                        height: 100%;
                        left: 0;
                        -o-object-fit: cover;
                        object-fit: cover;
                        pointer-events: none;
                        position: absolute;
                        top: 0;
                        width: 100%;
                        z-index: -2;
                    }

                    /* Style for section-deco image */
                    .section-cta .section-deco {
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        margin-left: 23.75rem;
                        width: 47.1875rem;
                    }
                    @media (max-width: 991.98px) {
                        .section-cta .section-deco {
                            position: static;
                            margin-left: 0;
                            width: 100%;
                            left: 0;
                            bottom: 0;
                            max-width: 100vw;
                            display: block;
                        }
                    }
            `}</style>
      </section>
    );
};

export default Game;
