// import { DefaultSession } from "next-auth"
import {DefaultJWT} from "next-auth/jwt"

declare module "next-auth" {
    interface Session {
        user: {
            accessToken?: string;
            userId?: number;
        } & User
    }

    export interface User extends DefaultUser {
        // Thêm các thuộc tính tùy chỉnh cho User nếu bạn lưu vào DB
        accessToken: string;
        id?: string; // Ví dụ
        user_id?: number;
        phone?: string; // Số điện thoại nếu cần
        roleId?: string; // ID của vai trò người dùng nếu cần
        email?: string;
        name?: string;
        avatar?: string;
    }
}

declare module "next-auth/jwt" {
    interface JWT extends DefaultJWT {
        id?: string; // Ví dụ
        accessToken?: string;
        userId?: number;
        phone?: string;
        name?: string;
        email?: string;
        avatar?: string;
    }
}

export interface UserResponse{
    accessToken: string;
    user_id: number;
    success: boolean;
    message: string,
    data: User
}
