/* Gallery Styles */
.gallery{
  margin-top: 1rem;
}

/* Typography Improvements */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
}

.page_my_media {
  font-feature-settings: "kern" 1, "liga" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5em;
  color: #1a202c;
}

p {
  margin-bottom: 1em;
  color: #4a5568;
}

/* Better text contrast */
.text-primary {
  color: #2b6cb0 !important;
}

.text-secondary {
  color: #4a5568 !important;
}

.text-muted {
  color: #718096 !important;
}

.text-light {
  color: #a0aec0 !important;
}

/* Improved readability */
.small-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

.medium-text {
  font-size: 1rem;
  line-height: 1.6;
}

.large-text {
  font-size: 1.125rem;
  line-height: 1.6;
}

.page_my_media {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.page-main {
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 60px);
  margin-top: 60px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 768px) {
  .container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 32px;
  }
}

/* Tabs */
.nav-tabs-photo {
  border-bottom: 2px solid #f1f3f4;
  margin-bottom: 0;
  padding: 20px 0 0;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.nav-tabs-photo .nav-link {
  background: none;
  border: none !important;
  color: #4a5568;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin-right: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  min-height: 48px;
}

.nav-tabs-photo .nav-link:hover {
  color: #2d3748;
  background: rgba(43, 108, 176, 0.08);
  transform: translateY(-1px);
}

.nav-tabs-photo .nav-link.active {
  color: #2b6cb0;
  background: linear-gradient(135deg, rgba(43, 108, 176, 0.1), rgba(102, 16, 242, 0.05));
  font-weight: 600;
  box-shadow: 0 2px 12px rgba(43, 108, 176, 0.15);
  position: relative;
  border: 1px solid rgba(43, 108, 176, 0.2);
}

.nav-tabs-photo .nav-link.active::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 40%;
  height: 3px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  border-radius: 2px;
  animation: tabIndicator 0.3s ease-out;
}

@keyframes tabIndicator {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 40%;
    opacity: 1;
  }
}

/* Mobile responsive tabs */
@media (max-width: 768px) {
  .nav-tabs-photo {
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .nav-tabs-photo::-webkit-scrollbar {
    display: none;
  }
  
  .nav-tabs-photo .nav-link {
    padding: 12px 20px;
    font-size: 14px;
    min-width: 120px;
    justify-content: center;
  }
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
  padding: 4px;
}

@media (min-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 24px;
  }
}

.gallery-grid-2 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

@media (min-width: 768px) {
  .gallery-grid-2 {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 16px;
  }
}

.gallery-grid-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.gallery-grid-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0, 123, 255, 0.15);
  z-index: 2;
}

.gallery-grid-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-grid-item.photo-select {
  border: 2px dashed #ccc;
  background: #f9f9f9;
}

.gallery-grid-item.photo-select svg {
  opacity: 0.6;
}

.gallery-grid-item.thumbnail {
  background: transparent;
}

.gallery-grid-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(28, 5, 59, 0.65);
  color: #906fb6;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

[class*="iconsvg"] {
  fill: currentColor;
  display: inline-block;
  height: 1em;
  width: 1em;
}

.iconsvg-play {
  font-size: 1.45455em;
}

.btn-photo {
  margin-top: 20px;
  gap: 10px;
}

.btn-gradient-border {
  background: linear-gradient(45deg, #FCC14B, #A2DD99, #92E7F1);
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s;
}

.btn-gradient-border:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.btn-gradient2 {
  background: linear-gradient(45deg, #FCC14B, #A2DD99);
  border: none;
  color: #333;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s;
}

.btn-photo-save {
  background: linear-gradient(45deg, #FCC14B, #A2DD99, #92E7F1);
  border-radius: 8px;
  padding: 12px;
  margin-top: 10px;
}

.badge-delete-container {
  position: absolute;
  top: 8px;
  right: 8px;
}

.badge-delete {
  background: rgba(255, 0, 0, 0.8);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.badge-delete:hover {
  background: rgba(255, 0, 0, 1);
}

.btn-delete-photo {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0,0,0,0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  z-index: 2;
  transition: background 0.15s;
}

.btn-delete-photo:hover {
  background: #ff4b4b;
}

.text-photo-notice {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.loader-ellips {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 20px;
}

.loader-ellips__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #007bff;
  animation: loader-ellips 1.4s infinite ease-in-out both;
}

.loader-ellips__dot:nth-child(1) { animation-delay: -0.32s; }
.loader-ellips__dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loader-ellips {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.text-yellow-2 {
  color: #FCC14B;
}

.fs-sm {
  font-size: 14px;
}

.fs-15px {
  font-size: 15px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  margin: 30px 0;
  border: 2px dashed #dee2e6;
  transition: all 0.3s ease;
}

.empty-state:hover {
  border-color: #007bff;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.02), rgba(102, 16, 242, 0.01));
}

.empty-state-icon {
  margin-bottom: 32px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.empty-state:hover .empty-state-icon {
  opacity: 0.8;
  transform: scale(1.1);
}

.empty-state-title {
  color: #495057;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #495057, #6c757d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-state-description {
  color: #6c757d;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 auto 32px;
  max-width: 400px;
}

.empty-state-cta {
  margin-top: 24px;
}

.empty-state-cta .btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  border: none;
}

.empty-state-cta .btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.empty-state-cta .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* Face Upload Section */
.face-upload-section {
  padding: 40px 20px;
}

.face-upload-container {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  background: white;
  padding: 60px 40px;
  border-radius: 24px;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.face-upload-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.02), rgba(102, 16, 242, 0.01));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.face-upload-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 45px rgba(0, 123, 255, 0.15);
}

.face-upload-container:hover::before {
  opacity: 1;
}

.face-upload-icon {
  margin-bottom: 32px;
  transition: all 0.3s ease;
  opacity: 0.7;
  position: relative;
  z-index: 1;
}

.face-upload-container:hover .face-upload-icon {
  opacity: 0.9;
  transform: scale(1.1);
}

.face-upload-title {
  color: #343a40;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.face-upload-description {
  color: #6c757d;
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 480px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

.face-upload-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .face-upload-container {
    padding: 40px 20px;
    margin: 0 16px;
  }
  
  .face-upload-title {
    font-size: 24px;
  }
  
  .face-upload-description {
    font-size: 16px;
  }
  
  .face-upload-buttons {
    flex-direction: column;
    align-items: center;
  }
}

.btn-gradient-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  color: white;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  min-width: 160px;
  justify-content: center;
  text-decoration: none;
}

.btn-gradient-primary:hover {
  background: linear-gradient(45deg, #0056b3, #004494);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
  color: white;
}

.btn-gradient-secondary {
  background: linear-gradient(45deg, #6c757d, #495057);
  border: none;
  color: white;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
  min-width: 160px;
  justify-content: center;
  text-decoration: none;
}

.btn-gradient-secondary:hover {
  background: linear-gradient(45deg, #495057, #343a40);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
  color: white;
}

/* Button icons */
.btn-gradient-primary svg,
.btn-gradient-secondary svg {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.btn-gradient-primary:hover svg,
.btn-gradient-secondary:hover svg {
  transform: scale(1.1);
}

/* Face Management Section */
.face-management-section {
  padding: 20px 0;
}

.face-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(45deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  border: 1px solid #dee2e6;
}

.face-count-badge {
  display: flex;
  align-items: center;
  gap: 5px;
  background: white;
  padding: 10px 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.face-count-number {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
}

.face-count-total {
  font-size: 16px;
  color: #6c757d;
}

.face-count-label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  margin-left: 5px;
}

.btn-edit-faces {
  background: transparent;
  border: 2px solid #007bff;
  color: #007bff;
  padding: 8px 16px;
  border-radius: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-edit-faces:hover {
  background: #007bff;
  color: white;
  transform: translateY(-1px);
}

.btn-edit-faces svg {
  fill: currentColor;
}

/* My Media Section */
.my-media-section {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 2px solid #e9ecef;
}

.my-media-header {
  text-align: center;
  margin-bottom: 25px;
}

.my-media-title {
  color: #343a40;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #007bff, #6610f2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.my-media-subtitle {
  color: #6c757d;
  font-size: 14px;
  margin: 0;
}

.my-media-empty {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px dashed #adb5bd;
}

.my-media-gallery .gallery-grid {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid #e9ecef;
}

/* Fancybox Custom Styles */
.fancybox__container {
  --fancybox-color: #fff;
  --fancybox-bg: rgba(0, 0, 0, 0.9);
  --fancybox-accent-color: #007bff;
}

.fancybox__toolbar {
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(102, 16, 242, 0.1));
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px;
  margin: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.fancybox__button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: 0 2px;
  color: white;
  transition: all 0.3s ease;
}

.fancybox__button:hover {
  background: rgba(0, 123, 255, 0.3);
  border-color: rgba(0, 123, 255, 0.5);
  transform: translateY(-1px);
}

.fancybox__button svg {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.fancybox__infobar {
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.2), rgba(102, 16, 242, 0.2));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  margin: 20px;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.fancybox__slide {
  padding: 40px;
}

.fancybox__content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

/* Gallery grid items hover effect for Fancybox */
.gallery-grid-item.thumbnail {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.gallery-grid-item.thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(102, 16, 242, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.gallery-grid-item.thumbnail:hover::before {
  opacity: 1;
}

.gallery-grid-item.thumbnail:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

.gallery-grid-item.thumbnail img {
  transition: transform 0.3s ease;
}

.gallery-grid-item.thumbnail:hover img {
  transform: scale(1.05);
}

/* Loading States */
.page-load-status {
  text-align: center;
  padding: 32px 20px;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

/* Modern loading skeleton */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 20px;
}

.skeleton-item {
  aspect-ratio: 1;
  border-radius: 12px;
}

/* Success/Error feedback */
.feedback-message {
  padding: 12px 20px;
  border-radius: 8px;
  margin: 16px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  animation: slideIn 0.3s ease-out;
}

.feedback-success {
  background: linear-gradient(135deg, #d4edda, #c3e6cb);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback-error {
  background: linear-gradient(135deg, #f8d7da, #f5c6cb);
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Micro-interactions and Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  70% {
    opacity: 0.9;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Apply animations to elements */
.page-main {
  animation: fadeInUp 0.6s ease-out;
}

.nav-tabs-photo .nav-link {
  animation: slideInFromLeft 0.4s ease-out;
  animation-fill-mode: both;
}

.nav-tabs-photo .nav-link:nth-child(2) {
  animation-delay: 0.1s;
}

.gallery-grid-item {
  animation: bounceIn 0.5s ease-out;
  animation-fill-mode: both;
}

.gallery-grid-item:nth-child(1) { animation-delay: 0.1s; }
.gallery-grid-item:nth-child(2) { animation-delay: 0.15s; }
.gallery-grid-item:nth-child(3) { animation-delay: 0.2s; }
.gallery-grid-item:nth-child(4) { animation-delay: 0.25s; }
.gallery-grid-item:nth-child(5) { animation-delay: 0.3s; }
.gallery-grid-item:nth-child(6) { animation-delay: 0.35s; }

.face-upload-container {
  animation: fadeInUp 0.8s ease-out;
}

.empty-state {
  animation: fadeInUp 0.6s ease-out;
}

/* Hover pulse effect for important buttons */
.btn-gradient-primary:hover,
.btn-gradient-secondary:hover {
  animation: pulse 2s infinite;
}

/* Loading shimmer effect */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth scrolling */
.gallery-grid {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
.btn-gradient-primary:focus,
.btn-gradient-secondary:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Authentication Prompt */
.auth-prompt .empty-state {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2px dashed #2196f3;
}

.auth-prompt .empty-state:hover {
  border-color: #1976d2;
  background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
}

.auth-prompt .empty-state-icon svg {
  color: #2196f3;
}

.auth-prompt .empty-state-title {
  background: linear-gradient(45deg, #1976d2, #2196f3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-prompt .btn-primary {
  background: linear-gradient(45deg, #2196f3, #1976d2);
  border: none;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.auth-prompt .btn-primary:hover {
  background: linear-gradient(45deg, #1976d2, #1565c0);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
      transform: translateY(-2px);
  }

/* Edit Mode Styles */
.edit-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-cancel-edit {
  background: linear-gradient(45deg, #6c757d, #495057);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-cancel-edit:hover {
  background: linear-gradient(45deg, #495057, #343a40);
  transform: translateY(-1px);
}

.btn-save-edit {
  background: linear-gradient(45deg, #28a745, #20c997);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-save-edit:hover {
  background: linear-gradient(45deg, #20c997, #1db584);
  transform: translateY(-1px);
}

.btn-save-edit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.face-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.face-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 2px solid #fff;
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

.face-checkbox input[type="checkbox"]:checked {
  background: #007bff;
  border-color: #007bff;
}

.selected-for-delete {
  opacity: 0.7;
  border: 3px solid #dc3545 !important;
  border-radius: 12px;
  transform: scale(0.95);
  transition: all 0.3s ease;
}

.badge-delete {
  transition: all 0.2s ease !important;
  z-index: 10;
}

.badge-delete:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.edit-actions {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.edit-actions .face-upload-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .edit-buttons {
    flex-direction: column;
    gap: 8px;
  }
  
  .btn-cancel-edit,
  .btn-save-edit {
    width: 100%;
    text-align: center;
  }
  
  .edit-actions .face-upload-buttons {
    flex-direction: column;
  }
}