function fbLogin() {
    console.log('fbLogin...'); //return;
    FB.login(function (response) {
        if (response.authResponse) {
            fetchFBProfile();
        } else {
            showError('Facebook login failed');
        }
    }, { scope: 'public_profile' });
}

function fetchFBProfile() {

    console.log('Fetching Facebook profile...'); //return;

    FB.api('/me', { fields: 'name,email,picture' }, async function (response) {
        if (!response || response.error) {
            showError('Failed to fetch profile');
            return;
        }

        // Gửi accessToken về backend xác thực (tuỳ cấu hình <PERSON> backend bạn)
        const accessToken = FB.getAuthResponse().accessToken;
        const browser = navigator.userAgent;

        try {
            const ip = await fetch('https://api.ipify.org?format=json').then(res => res.json());
            const serverResponse = await fetch(apiServerAdmClient + '/auth/facebook', {
                //const result = await fetch('http://localhost:8080/papi/v1/auth/facebook', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    accessToken: accessToken,
                    browser: browser,
                    ipAddress: ip.ip
                })
            });
            const data = await serverResponse.json();

            console.log('Server response:', data);

            if (!serverResponse.ok) {
                throw new Error(data.error || 'Authentication failed');
            }

            console.log('Authentication successful:', accessToken);

            try {
                // After successful authentication, get profile from Google token
                const decodedToken = jwt_decode(accessToken);
                const userProfile = {
                    name: decodedToken.name,
                    email: decodedToken.email,
                    picture: decodedToken.picture
                };
                console.log('decodedToken', decodedToken);
                console.log('User profile:', userProfile);

                if (!isValidProfile(userProfile)) {
                    throw new Error('Invalid profile data from Google');
                }

                // Save Google profile info to localStorage
                localStorage.setItem('userProfile', JSON.stringify(userProfile));

                // Show profile section
                showProfile('userProfile', userProfile);

                // save to cookie
                $.cookie('userToken', data.data.sessionToken, { expires: 7, path: '/' });
                $.cookie('userId', data.data.userId, { expires: 7, path: '/' });
            } catch (err) {
            }



            console.log('call syncApiLogin');

            syncApiLogin(data.data.userId, data.data.sessionToken);

            //showProfile(response);
        } catch (err) {
            //showError(err.message);
        }
    });
}
