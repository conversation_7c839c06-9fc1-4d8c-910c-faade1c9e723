﻿async function handleCredentialResponseAdm(tokenCredential) {
    try {
        const browser = getBrowserInfo();
        const ipAddress = await getIPAddress();

        console.log('handleCredentialResponse response', tokenCredential);

        // Send the token to our server for verification
        const serverResponse = await fetch('https://dev-idol14-api.net-solutions.vn/v1' + '/auth/google', {
            // const serverResponse = await fetch('http://***********:8000/adopt/idol14/papi/v1/auth/google', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                token: tokenCredential.credential,
                browser: browser,
                ipAddress: ipAddress
            })
        });

        const data = await serverResponse.json();

        console.log('Server response:', data);

        if (!serverResponse.ok) {
            throw new Error(data.error || 'Authentication failed');
        }

        window.postMessage(
            {
                accessToken: data.data.sessionToken,
            },
            '*'
        );

    } catch (error) {
        console.error('Error:', error);
    }
}

function setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days*24*60*60*1000)); // Tính thời gian hết hạn
    const expires = "expires=" + date.toUTCString();
    document.cookie = `${name}=${value}; ${expires}; path=/`;
}
function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browser = "Unknown";

    if (userAgent.indexOf("Chrome") > -1) {
        browser = "Chrome";
    } else if (userAgent.indexOf("Firefox") > -1) {
        browser = "Firefox";
    } else if (userAgent.indexOf("Safari") > -1) {
        browser = "Safari";
    } else if (userAgent.indexOf("Edge") > -1) {
        browser = "Edge";
    } else if (userAgent.indexOf("MSIE") > -1 || userAgent.indexOf("Trident/") > -1) {
        browser = "Internet Explorer";
    }

    return browser;
}

// Function to get IP address
async function getIPAddress() {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.error('Error getting IP:', error);
        return '127.0.0.1'; // Fallback to localhost
    }
}




