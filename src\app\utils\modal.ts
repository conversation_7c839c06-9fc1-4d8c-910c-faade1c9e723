export const getOrCreateModalInstance = (modalId: string) => {
    const modalElement = document.getElementById(modalId);
    /* eslint-disable  @typescript-eslint/no-explicit-any */
    if (!modalElement || typeof window === 'undefined' || !(window as any).bootstrap) {
        return null;
    }
    
    /* eslint-disable  @typescript-eslint/no-explicit-any */
    return (window as any).bootstrap.Modal.getInstance(modalElement) || new (window as any).bootstrap.Modal(modalElement);
};

export const showModal = (modalId: string) => {
    const modal = getOrCreateModalInstance(modalId);
    modal?.show();
};

export const hideModal = (modalId: string) => {
    const modal = getOrCreateModalInstance(modalId);
    modal?.hide();
};