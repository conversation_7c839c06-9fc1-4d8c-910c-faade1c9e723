import { useEffect } from 'react';
import Script from 'next/script';
import {signIn} from "next-auth/react";
declare global {
    function handleCredentialResponseAdm(response: string): void;
}
interface ClientId {
    clientId: string;

}
const GoogleSignIn = ({ clientId} :ClientId) => {
    return (
        <>
            <Script src="https://accounts.google.com/gsi/client" strategy="afterInteractive" />
            <div className="login-btn login-fb" style={{margin:"0px auto"}}>
                <div
                    id="g_id_onload"
                    data-client_id={clientId}
                    data-callback="handleCredentialResponseAdm"
                />
                <div
                    className="g_id_signin"
                    data-type="standard"
                    data-size="large"
                    data-theme="outline"
                    data-text="sign_in_with"
                    data-shape="rectangular"
                    data-logo_alignment="left"
                />
            </div>
        </>
    );
};

export default GoogleSignIn;
