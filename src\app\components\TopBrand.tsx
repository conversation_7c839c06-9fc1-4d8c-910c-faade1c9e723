'use client';

import { useState, useEffect } from 'react';
import { API_ENDPOINTS } from '../utils/api-endpoints';
import commonFunc from '../utils/common';

interface Brand {
    title: string | null;
    description: string | null;
    link: string;
    logo: string | null;
    image: string | null;
}

const TopBrand = () => {
    const [brands, setBrands] = useState<Brand[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const fetchBrands = async () => {
        try {
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.brandFeatured}`, { headers });
            const data = await res.json();
            if (data.status) {
                setBrands(data.data);
            } else {
                setError(data.message);
            }
        } catch (err) {
            setError('Failed to fetch brands. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchBrands();
    }, []);

    if (isLoading) {
        return <div className="flex justify-center items-center h-64"><div className="loader"></div><p className="ml-4 text-gray-400">Loading Brands...</p></div>;
    }

    if (error) {
        return <div className="text-red-500 text-center p-4">{error}</div>;
    }

    return (
        <section className="section-hero section text-white" style={{backgroundImage: "url('images/home/<USER>/bg.webp')"}}>
            <div className="container container-lg-fluid d-flex flex-column gap-4">
                <h1 className="section-title text-uppercase text-center mb-0">
                    <span className="visually-hidden">Khối doanh nghiệp</span>
                    <img className="img-fluid" src={"/images/home/<USER>/title.svg"} alt="" />
                </h1>
                <div className="flex-center-x">
                    <span className="text-fandom d-inline-flex">
                        <span className="visually-hidden">Fandom yêu nước</span>
                        <img className="img-fluid" src={"/images/home/<USER>/text-fandom.webp"} alt="" />
                    </span>
                </div>
                <div className="logos mx-auto">
                    {brands.map((brand, index) => (
                        <a key={index} className="logos-item" href={brand.link || '#'}>
                            <img className="img-fluid" src={brand.logo ||`/images/home/<USER>/logos/logo-${index + 1}.webp`} alt={brand.title || ''} />
                        </a>
                    ))}
                </div>
                <p className="section-lead text-center mx-auto mb-0">
                    vinh danh những sản phẩm {"Made in Vietnam"} sáng tạo, ý nghĩa, góp phần lan tỏa tinh thần yêu nước
                    nhân dịp lễ kỷ niệm 80 năm Cách mạng tháng Tám thành công và
                    Quốc khánh nước Cộng hòa Xã hội Chủ nghĩa Việt Nam.
                </p>
            </div>
        </section>
    );
}

export default TopBrand;
