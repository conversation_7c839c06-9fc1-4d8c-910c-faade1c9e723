'use client';

interface IntroSectionProps {
    title: string;
    subtitle: string;
    description: string;
}

const IntroSection = ({ title, subtitle, description }: IntroSectionProps) => {
    return (
        <section className="intro">
            <div className="container container-lg-fluid">
                <div className="intro-body">
                    <div className="row align-items-center gy-3">
                        <div className="col-xxl-auto">
                            <h2 className="intro-title fw-medium text-uppercase mb-0">
                                <span className="text-gradient-base d-inline-block">{title}</span>
                                <span className="intro-title-sub">
                                    <span className="text-gradient-base">{subtitle}</span>
                                    <span className="text-yeu-nuoc">
                                        <img src={"images/home/<USER>/text-yeu-nuoc.webp"} alt="Yêu nước" />
                                    </span>
                                </span>
                            </h2>
                        </div>
                        <div className="col-xxl">
                            <p className="mb-0">{description}</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default IntroSection;
