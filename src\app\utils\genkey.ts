import {API_KEY, API_SECRET} from "@/app/utils/api-endpoints";
import * as crypto from 'crypto';
export  const generateApiHeaders = ( body = {}) => {
    const apiKey    = API_KEY;
    const apiSecret =  API_SECRET;
    const timestamp = Math.floor(Date.now() / 1000).toString(); // UNIX timestamp (giây)
    const raw = `${timestamp}.${JSON.stringify(body)}`;
    const signature = crypto.createHmac('sha256', apiSecret).update(raw).digest('hex');

    return {
        'x-api-key': apiKey,
        'x-signature': signature,
        'x-timestamp': timestamp,
    };
}

