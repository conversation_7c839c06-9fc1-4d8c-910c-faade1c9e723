.game-container{
    background: url('/img/bg.png');
    width: 100vw;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 780px;
}

.fs-14px{
    font-size: 14px;
}

.mx-30px{
    margin: 30px 0;
}

.px-30px{
    padding: 0 30px;
}

.py-30px{
    padding: 30px 0;
}

.pt-30px{
    padding-top: 30px;
}
.mb-30px{
    margin-bottom: 30px;
}

.gap-30px{
    gap: 30px;
}

.color-gold{
    background: -webkit-linear-gradient(#FAEEDD, #E9AF62, #F2D4AC, #FFC77D, #FDF0DF );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text-gold {
  background: linear-gradient(
    to right,
    #FAEEDD 0%,
    #E9AF62 25%,
    #F2D4AC 52%,
    #FFC77D 80%,
    #FDF0DF 100%
  );
   -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-text-gold2 {
  background: linear-gradient(
    to right,
    #FFFCF4 0%,
    #FFECB2 17%,
    #FFE9AE 33%,
    #FFCB82 50%,
    #FFECAE 67%,
    #FFF7DE 83%,
    #FFFFFF 100%
  );
   -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}


.swiper-container{
    background-color: #95000030;
    padding: 0.625rem;
    margin-bottom: 0.625rem;
    border-radius: 0.5rem;
}

.wish-container{
    background-color: #ffffff20;
    border-radius: 24px;
    padding: 0px 10px;
}

.wish-prize{
    background-color: #ffffff20;
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
}

.c-white-60{
    color: #ffffff60
}

.wish-sample{
    border-radius: 1.5rem;
    padding: 10px 16px;
    border: 1px solid #FFFFFF20;
}

.wish-form{
    background: url('/img/frame.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    height: 340px;
    margin-top: 10px;
    position: relative;
    width: 628px;
    margin: auto;
    min-width: 628px;
}

.wish-form2{
    background: url('/img/frame2.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    height: 300px;
    position: relative;
    width: 568px;
    min-width: 550px;
}

.wish-form-ta{
    max-width: 343px;
    background-color: #950000;
    position: absolute;
    border-radius: 0.5rem;
    border: 1px solid #FFFFFF30 !important;
    height: 92px;
    top: 125px;
    right: 30px;
    width: 100%;
    padding: 0.375rem 0.375rem 0.375rem 0.75rem;
}

.wish-form-ct{
    max-width: 310px;
    position: absolute;
    top: 105px;
    width: 100%;
    padding: 0.375rem 0.375rem 0.375rem 0.75rem;
    right: 32px
}

.wish-form-ct-content{
    font-size: 15px;
}

.wish-form-ct-name{
    font-size: 12px
}
.wish-form-ta textarea{
    padding: 0;
    border: 0;
    caret-color: #FFCB82
}
.wish-form-ta textarea:focus{
    background-color: #950000;
    box-shadow: none;
    caret-color: #FFCB82;
    color: transparent
}

.wish-form-name{
    position: absolute;
    bottom: -40px;
}

.custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* WebKit (Chrome, Safari, Edge) */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}

.custom-scroll::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.btn-send{
    display: flex;
    gap: 12px;
    background-color: #C92027;
    border-radius: 1rem;
    padding: 12px 24px;
    box-shadow: 0px 4px 0px 0px #8D0006;
    color: #FFFFFF;
    font-size: 17px;
    font-weight: 600
}

.wish-form-avatar{
    width: 142px;
    height: auto;
    aspect-ratio: 1/1;
    border-radius: 50%;
    position: absolute;
    top: 10px;
    left: 34px;
}

.lc-noi-bat-title{
    font-size: 30px;
    font-weight: bold;
    padding-left: 65px;
    padding-top: 17px;
}

.lc-noi-bat-card{
    min-width: 435px;
    width: 435px;
    border-radius: 1rem;
    background-color: #FF950050;
    backdrop-filter: blur(50px);
    -webkit-backdrop-filter: blur(50px);
    box-shadow: 0px 4px 12px 0px #FF440025;
    padding: 24px;
}

.lc-noi-bat-card-left{

}

.lc-noi-bat-card-right{
    position: relative;
}

.lc-noi-bat-card-avatar{
    width: 60px;
    height: auto;
    aspect-ratio: 1/1;
    border-radius: 50%;
}

.lc-noi-bat-card-name{
    margin-top: 12px;
    font-weight: bold;
    font-size: 12px;
}

.nhay-kep-nguoc, .nhay-kep{
    width: 20px;
    height: auto;
}

.lc-noi-bat-card-content{
    padding-top: 24px;
    font-size: 15px;
}

.nhay-kep{
    right: 0;
    bottom: 0;
}

.lc-noi-bat-card-2{
    top: -40px;
    left: 40%;
}

.lc-noi-bat-card-3{
    top: 71px;
}

.total-wish{
    background: linear-gradient(
        to right,
        #FFFF10 0%,
        #FF5912 50%,
        #FFF50D 79%,
        #FF5712 100%
    );
    border-radius: 16px;
    padding: 6px 20px;
    color: #CB0000;
    font-size: 13px;
    font-weight: bold;
    bottom: 0;
}

.wish-prize-thank{
    margin-top: 61px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
}