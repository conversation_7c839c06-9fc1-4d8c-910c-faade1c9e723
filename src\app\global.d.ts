// global.d.ts

// --- Interfaces cho Options (ví dụ cơ bản) ---
export interface HeadroomOptions {
  offset?: number | { up?: number; down?: number; };
  tolerance?: number | { up?: number; down?: number; };
  classes?: {
    initial?: string;
    pinned?: string;
    unpinned?: string;
    top?: string;
    notTop?: string;
    bottom?: string;
    notBottom?: string;
    frozen?: string;
  };
  scroller?: HTMLElement | Window;
  onPin?: () => void;
  onUnpin?: () => void;
  onTop?: () => void;
  onNotTop?: () => void;
  onBottom?: () => void;
  onNotBottom?: () => void;
  [key: string]: unknown; // Cho các options khác không liệt kê
}

export interface SwiperOptions {
  navigation?: boolean | {
    nextEl?: string | HTMLElement | null;
    prevEl?: string | HTMLElement | null;
    disabledClass?: string;
    hiddenClass?: string;
    lockClass?: string;
  };
  pagination?: boolean | {
    el?: string | HTMLElement | null;
    type?: 'bullets' | 'fraction' | 'progressbar' | 'custom';
    clickable?: boolean;
    dynamicBullets?: boolean;
    dynamicMainBullets?: number;
    renderBullet?: (index: number, className: string) => string;
    // ... nhiều options khác cho pagination
  };
  slidesPerView?: number | 'auto';
  spaceBetween?: number;
  loop?: boolean;
  centeredSlides?: boolean;
  speed?: number;
  allowTouchMove?: boolean;
  autoplay?: boolean | {
    delay?: number;
    disableOnInteraction?: boolean;
    pauseOnMouseEnter?: boolean;
  };
  // Thêm các Swiper options phổ biến khác bạn dùng
  [key: string]: unknown; // Cho các options khác
}

interface BootstrapModalOptions {
  backdrop?: boolean | 'static';
  keyboard?: boolean;
  focus?: boolean;
  [key: string]: unknown;
}

interface BootstrapTooltipOptions {
  animation?: boolean;
  container?: string | false | Element;
  delay?: number | { show: number; hide: number };
  html?: boolean;
  placement?: 'auto' | 'top' | 'bottom' | 'left' | 'right' | (() => void) | string;
  selector?: string | false;
  template?: string;
  title?: string | Element | (() => string);
  trigger?: 'click' | 'hover' | 'focus' | 'manual' | string; // 'click hover'
  offset?: [number, number] | string | (() => [number, number]);
  fallbackPlacements?: string[];
  boundary?: string | Element;
  customClass?: string | (() => string);
  sanitize?: boolean;
  allowList?: Record<string, unknown[]>; // A more specific type for allowList might be needed
  sanitizeFn?: (() => void) | null;
  popperConfig?: Partial<Record<string, unknown>> | null | (() => Partial<Record<string, unknown>>); // Type from Popper.js
  [key: string]: unknown;
}

// Popover options thường kế thừa và mở rộng Tooltip options
interface BootstrapPopoverOptions extends BootstrapTooltipOptions {
  content?: string | Element | (() => string);
  // Popover-specific options
}

interface VanillaOTPOptions {
  container?: HTMLElement; // Hoặc selector
  inputs?: number;
  numeric?: boolean;
  // Thêm các options khác nếu VanillaOTP hỗ trợ
  [key: string]: unknown;
}


// --- Interfaces cho Instances của thư viện ---
export interface HeadroomInstance {
  init: () => void;
  destroy: () => void;
  pin: () => void;
  unpin: () => void;
  freeze: () => void;
  unfreeze: () => void;
  // Thêm các phương thức và thuộc tính khác nếu cần
}

export interface SwiperInstance {
  el: HTMLElement;
  slides: HTMLElement[];
  activeIndex: number;
  // Methods
  slideNext: (speed?: number, runCallbacks?: boolean) => void;
  slidePrev: (speed?: number, runCallbacks?: boolean) => void;
  slideTo: (index: number, speed?: number, runCallbacks?: boolean) => void;
  update: () => void;
  destroy: (deleteInstance?: boolean, cleanStyles?: boolean) => void;
  on: (event: string, handler: (...args: unknown[]) => void) => void;
  off: (event: string, handler: (...args: unknown[]) => void) => void;
  // Thêm các thuộc tính và phương thức khác
  [key: string]: unknown; // Cho phép các thuộc tính/phương thức động khác
}

interface BootstrapModalInstance {
  show: () => void;
  hide: () => void;
  toggle: () => void;
  dispose: () => void;
  handleUpdate: () => void; // Thường có để cập nhật vị trí modal
  // getInstance: (element: Element) => BootstrapModalInstance | null; // static method
  // getOrCreateInstance: (element: Element, config?: Partial<BootstrapModalOptions>) => BootstrapModalInstance; // static method
}

interface BootstrapTooltipInstance {
  show: () => void;
  hide: () => void;
  toggle: () => void;
  dispose: () => void;
  enable: () => void;
  disable: () => void;
  toggleEnabled: () => void;
  update: () => void; // Thường có để cập nhật vị trí
}

// PopoverInstance is not needed as it does not add any new members to BootstrapTooltipInstance

export interface VanillaOTPInstance {
  // Giả sử VanillaOTP có các phương thức này, bạn cần kiểm tra tài liệu của nó
  getValue: () => string;
  setValue: (value: string) => void;
  focus: (index?: number) => void;
  clear: () => void;
  destroy?: () => void; // Nếu có phương thức destroy
  [key: string]: unknown;
}


// --- Khai báo global Window interface ---
declare global {
  interface Window {
    Headroom?: new (element: HTMLElement, options?: Partial<HeadroomOptions>) => HeadroomInstance;

    Swiper?: new (container: string | HTMLElement, options?: Partial<SwiperOptions>) => SwiperInstance;

    bootstrap?: {
      Modal?: new (element: Element | string, options?: Partial<BootstrapModalOptions>) => BootstrapModalInstance;
      Tooltip?: new (element: Element | string, options?: Partial<BootstrapTooltipOptions>) => BootstrapTooltipInstance;
      Popover?: new (element: Element | string, options?: Partial<BootstrapPopoverOptions>) => BootstrapPopoverInstance;
      // Khai báo các static methods nếu cần, ví dụ:
      // Modal: {
      //   getInstance: (element: Element) => BootstrapModalInstance | null;
      //   getOrCreateInstance: (element: Element, config?: Partial<BootstrapModalOptions>) => BootstrapModalInstance;
      // }
      [key: string]: unknown; // Cho các component khác của Bootstrap không được liệt kê rõ ràng
      Toast: {
        getOrCreateInstance: (
          element: HTMLElement,
          options?: Partial<{
            animation: boolean;
            autohide: boolean;
            delay: number;
          }>
        ) => {
          show: () => void;
          hide: () => void;
          dispose: () => void;
        };
      };
    };

    VanillaOTP?: new (element: HTMLElement, options?: Partial<VanillaOTPOptions>) => VanillaOTPInstance;

    // Pickr (ví dụ nếu bạn dùng)
    // Pickr?: {
    //   create: (options: Partial<PickrOptionsInterface>) => PickrInstanceInterface;
    // };
  }
}

// Cần thêm export {} ở cuối file để TypeScript coi đây là một module
// và áp dụng `declare global`. Nếu không có, nó có thể được coi là script toàn cục
// và `declare global` có thể không hoạt động như mong đợi trong một số trường hợp.
export {};