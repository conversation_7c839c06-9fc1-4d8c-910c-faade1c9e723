'use client';
import {useState, useEffect} from "react";
import {generateApiHeaders} from "../utils/genkey";
import Header from "@/app/components/Header";

interface Product {
    name: string;
    image: string;
    link: string | null;
    brand_name: string | null;
    brand_logo: string | null;
}

const Page = () => {
    let initialSearch = '';
    if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        initialSearch = urlParams.get('key') || '';
    }
    const [keyword, setKeyword] = useState(initialSearch);
    const [results, setResults] = useState([]);
    const [loading, setLoading] = useState(false);
    const [totalPages, setTotalPages] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);

    const handleSearch = async (page = 1) => {
        setLoading(true);
        setCurrentPage(page);
        try {
            const headers = generateApiHeaders();
            const response = await fetch(`https://dev.fandom.kamgift.vn/api-v1/product/search?page=${page}&limit=2&keyword=${keyword}`,
                {
                    method: 'GET',
                    headers: headers
                }
            );
            const result = await response.json();
            console.log('data', result.data);
            setResults(result.data.items);
            setTotalPages(result.data.totalPages);
            console.log('data', totalPages);
        } catch (error) {
            console.error("Failed to fetch search results:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (initialSearch) {
            handleSearch(1);
        }
    }, [initialSearch]);

    return (
        <div className="page home">
            <Header/>
            <section className="section-hero section text-white"
                     style={{backgroundImage: "url('images/home/<USER>/bg.webp')"}}>
                <input
                    type="text"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    placeholder="Nhập từ khóa tìm kiếm"
                />
                <button onClick={() => handleSearch(1)} disabled={loading}>
                    {loading ? 'Đang tìm...' : 'Tìm Kiếm'}
                </button>

                {loading && <p>Loading...</p>}
                { results.length > 0 && !loading ? (
                    <>
                        <div className="product-grid">
                            {results.map((product : Product, index :number) => (
                                <div key={index} className="card card-product" style={{width: '100%'}}>
                                    <a className="thumbnail thumbnail-hover" href={product.link || '#'}>
                                <span className="thumbnail-inner">
                                    <img className="thumbnail-img" src={product.image} alt={product.name}/>
                                </span>
                                    </a>
                                    <div className="card-body">
                                        <div className="flex-center-y gap-2">
                                    <span className="avatar">
                                        <img className="img-fluid w-100 avatar-img"
                                             src={product.brand_logo || 'images/ex/home/<USER>/logo.webp'} alt=""/>
                                    </span>
                                            {product.brand_name || 'Brand'}
                                        </div>
                                        <h3 className="card-title mb-0">
                                            <a href={product.link || '#'}>{product.name}</a>
                                        </h3>
                                        <span className="card-action">
                                    <button className="btn btn-secondary btn-square rounded-circle" type="button">
                                        <svg className="iconsvg-arrow-right">
                                            <use xlinkHref={"images/sprite.svg#arrow-right" }></use>
                                        </svg>
                                    </button>
                                </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                        {totalPages > 1 && (
                            <div>

                                {Array.from({length: totalPages}, (_, i) => i + 1).map((page) => (
                                    <button
                                        key={page}
                                        onClick={() => handleSearch(page)}
                                        disabled={loading || currentPage === page}
                                        style={{
                                            fontWeight: currentPage === page ? 'bold' : 'normal',
                                            margin: '0 5px'
                                        }}
                                    >
                                        {page}
                                    </button>
                                ))}
                            </div>
                        )}
                    </>
                    ) :(
                    <div>
                        Không tìm thấy kết quả
                    </div>
                ) }
            </section>
        </div>
    )
}
export default Page;
