'use client';

import { useState, useEffect } from 'react';
import { API_ENDPOINTS } from '../utils/api-endpoints';
import commonFunc from '../utils/common';

interface Post {
    title: string;
    description: string;
    link: string;
    image: string;
    brand_name: string | null;
    brand_logo: string | null;
}

const TopListFandom = () => {
    const [posts, setPosts] = useState<Post[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    const fetchPosts = async () => {
        try {
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.postTop}`, { headers });
            const data = await res.json();
            if (data.status) {
                setPosts(data.data);
            } else {
                setError(data.message);
            }
        } catch (err) {
            setError('Failed to fetch posts. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchPosts();
    }, []);

    if (isLoading) {
        return <div className="flex justify-center items-center h-64"><div className="loader"></div><p className="ml-4 text-gray-400">Loading Posts...</p></div>;
    }

    if (error) {
        return <div className="text-red-500 text-center p-4">{error}</div>;
    }

    return (
        <section>
            <div className="container container-lg-fluid">
                <div className="product-grid">
                    {posts.map((post, index) => (
                        <div key={index} className="card card-product card-product-campaign">
                            <a className="thumbnail thumbnail-hover" href={post.link}>
                                <span className="thumbnail-inner">
                                    <img className="thumbnail-img" src={post.image} alt={post.title} />
                                </span>
                            </a>
                            <div className="card-body">
                                <div className="flex-center-y gap-2">
                                    <span className="avatar">
                                        <img className="img-fluid w-100 avatar-img" src={post.brand_logo || '/images/ex/home/<USER>/logo.webp'} alt="" />
                                    </span>
                                    {post.brand_name || 'Brand'}
                                </div>
                                <h3 className="card-title mb-0">
                                    <a href={post.link}>{post.title}</a>
                                </h3>
                                <span className="card-action">
                                    <button className="btn btn-secondary btn-square rounded-circle" type="button">
                                        <svg className="iconsvg-arrow-right">
                                            <use xlinkHref={"/images/sprite.svg#arrow-right"}></use>
                                        </svg>
                                    </button>
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}

export default TopListFandom;
