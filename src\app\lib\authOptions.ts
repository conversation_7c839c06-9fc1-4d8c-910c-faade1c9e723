// lib/authOptions.ts
import NextAuth, {CredentialsSignin, User} from "next-auth";
import Credentials from "next-auth/providers/credentials";

import {API_ENDPOINTS} from "@/app/utils/api-endpoints";
class InvalidLoginError extends CredentialsSignin {
  code = "custom";
  constructor(message: string) {
    super(message);
    this.code = message;
  }
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Credentials({
      id: "credentials",
      name: "credentials",
      credentials: {
        acc_fandom: { label: "Token", type: "text" },
      },
      authorize: async (credentials) => {

        try {
          console.log('Authorizing with token:', credentials.acc_fandom);

          if (!credentials.acc_fandom) {
            throw new Error("Token is required");
          }

          const myHeaders = new Headers();
          myHeaders.append("Content-Type", "application/x-www-form-urlencoded");

          const urlencoded = new URLSearchParams();
          urlencoded.append("token", credentials.acc_fandom as string);

          const requestOptions: RequestInit = {
            method: "POST",
            headers: myHeaders,
            body: urlencoded,
            redirect: "follow"
          };

          const response = await fetch(
              `${API_ENDPOINTS.login}`,
              requestOptions
          );

          if (!response.ok) {
            throw new Error(`API call failed with status: ${response.status}`);
          }

          const tk = await response.json();
          console.log(tk);

          if (tk?.status !== true) {
            throw new Error("Authentication failed: " + (tk?.message || "Unknown error"));
          } else {
            const user: User = {
              accessToken: credentials.acc_fandom as string,
              name: tk.data?.name,
              email: tk.data?.email,
              phone: tk.data?.phone,
              avatar: tk.data?.avatar,
            };
            return user;
          }

          // Create user object with all required properties
        } catch (error) {
          // console.error('Authorization error:', error);
          throw new InvalidLoginError((error as { message: string }).message);
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ account }) {
      // Cho phép đăng nhập với credentials
      if (account?.provider === "credentials") {
        return true;
      }
      // Có thể thêm logic cho các provider khác ở đây
      return false;
    },
    async jwt({ token, account, user, trigger, session }) {
      // Lần đầu đăng nhập, lưu thông tin user vào token
      if (account && user) {
        token.accessToken = user.accessToken;
        token.name = user.name;
        token.phone = user.phone;
        token.userId = user.user_id;
        token.email = user.email;
        token.avatar = user.avatar;
      }
      // Cho phép cập nhật token từ client
      if (trigger === "update" && session) {
        if (session.contact_phone !== undefined) {
          token.contact_phone = session.contact_phone;
        }
        if (session.contact_phone_authened !== undefined) {
          token.contact_phone_authened = session.contact_phone_authened;
        }
        if (session.is_update_info !== undefined) {
          token.is_update_info = session.is_update_info;
        }
      }
      return token;
    },
    async session({ session, token }) {
      // Gán thông tin từ token vào session
      if (session.user && token.accessToken) {
        session.user.accessToken = token.accessToken;
        session.user.userId = token.userId;
        session.user.name = token.name;
        session.user.phone = token.phone;
        session.user.email = token.email || "";
        session.user.avatar = token.avatar || "";
      }
      return session;
    },
  },
  pages: {
    signIn: "/login",
  },
  trustHost: true,
  secret: process.env.AUTH_SECRET,
  debug: process.env.NODE_ENV === "production", // Bật debug mode
});
