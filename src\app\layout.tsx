import type {Metada<PERSON>} from "next";
import {<PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono} from "next/font/google";
import "./globals.css";
import "../../public/css/style.min.css"
import {NextAuthProvider} from "@/app/components/Providers";
import Script from "next/script";
import { GlobalInitializers } from "./components/GlobalInitializers";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "Home - Toplist Fandom yêu nước",
    description: "Toplist Fandom yêu nước",
    keywords: "HTML5 Template",
    authors: [{ name: "TUTA" }],
    viewport: "width=device-width, initial-scale=1",
    icons: {
        icon: "/images/favicon.ico",
        shortcut: "/images/favicon.ico",
    },
};

export default function RootLayout({
                                       children,
                                   }: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="vi">
        <body className={`${geistSans.variable} ${geistMono.variable}`} suppressHydrationWarning>
        <NextAuthProvider>
           {children}
        </NextAuthProvider>
        <Script src={ "/js/login-fb.js"} strategy="beforeInteractive" />
        <Script src={ "/js/login-lib.js"} strategy="beforeInteractive" />
        <Script src={ process.env.SUB +"/js/login-gg.js"} strategy="beforeInteractive" />
        <Script src="https://unpkg.com/jwt-decode@3.1.2/build/jwt-decode.js" strategy="beforeInteractive" />
        <Script src={ process.env.SUB +"/js/vendor/bootstrap.bundle.min.js" } strategy="beforeInteractive" />
        <Script src={ "/js/vendor/headroom.min.js"}/>
        <GlobalInitializers/>
        </body>
        </html>
    );
}


