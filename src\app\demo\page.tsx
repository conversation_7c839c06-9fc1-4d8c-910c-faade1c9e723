'use client'
import React, { useRef, useState, useEffect, useCallback } from "react";
import Swal from 'sweetalert2';
import { useSession } from "next-auth/react";
import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import Header from "@/app/components/Header";
import commonFunc from "@/app/utils/common";
import './gallery.css';
import { API_ENDPOINTS } from "../utils/api-endpoints";

const MAX_PHOTOS = 5;
const EVENT_ID = '1'; // Real event ID

interface MediaItem {
  id: number;
  type: number; // 0 = image, 1 = video
  url: string;
  thumb: string;
  duration: string;
  created_at: string;
}

interface FaceItem {
  face_id: string;
  url: string;
}

interface MediaResponse {
  status: boolean | number;
  error_code: number;
  message: string;
  data: {
    items?: MediaItem[];
    data?: MediaItem[];
    total: number;
    per_page?: number;
    perPage?: number;
    current_page?: number;
    currentPage?: number;
    last_page?: number;
    totalPages?: number;
    orgpath?: string;
  };
}

const Page = () => {
    const { data: session } = useSession();
    const [activeTab, setActiveTab] = useState<'event' | 'my'>('event'); // Bắt đầu với tab "Ảnh sự kiện"
    const [faces, setFaces] = useState<FaceItem[]>([]);
    const [eventMediaData, setEventMediaData] = useState<MediaItem[]>([]);
    const [myMediaData, setMyMediaData] = useState<MediaItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(true);
    const [hasLoadedInitial, setHasLoadedInitial] = useState(false);
    const [isUploadingFace, setIsUploadingFace] = useState(false);
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [previewImages, setPreviewImages] = useState<string[]>([]);
    const [isEditingFaces, setIsEditingFaces] = useState(false);
    const [selectedFacesToDelete, setSelectedFacesToDelete] = useState<string[]>([]);
    
    const fileInputRef = useRef<HTMLInputElement>(null);
    const cameraInputRef = useRef<HTMLInputElement>(null);
    const galleryRef = useRef<HTMLDivElement>(null);

    // Load faces và event media on component mount
    useEffect(() => {
        if (session) {
            loadFaces();
        }
        loadEventMedia(1, true); // Load event media đầu tiên
        
        // Initialize Fancybox
        Fancybox.bind("[data-fancybox]");

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [session]); // Chạy khi session thay đổi

    // Cleanup Fancybox on unmount
    useEffect(() => {
        return () => {
            Fancybox.destroy();
        };
    }, []);

    const loadFaces = async () => {
        try {
            if (!session?.user.accessToken) return;
            // Call real API
            const res = await fetch(API_ENDPOINTS.faceList, {
                headers: {
                    'authorization': `Bearer ${session.user.accessToken}`,
                }
            });
            const data = await res.json();
            if (data.status === 1 && data.data) {
                setFaces(data.data);
                setHasLoadedInitial(false); // Reset flag khi load faces mới
            } else if (data.status === 0) {
                await Swal.fire({ icon: 'error', title: 'Không thể tải ảnh nhận diện', text: data.message || 'Vui lòng thử lại sau.' });
            }
        } catch (error) {
            console.error('Error loading faces:', error);
            // Fallback to demo API if real API fails
            try {
                const res = await fetch('/api/demo/get-faces');
                const data = await res.json();
                if (data.status === 1) {
                    setFaces(data.data || []);
                    setHasLoadedInitial(false);
                } else {
                    await Swal.fire({ icon: 'error', title: 'Không thể tải dữ liệu demo', text: data.message || 'Vui lòng thử lại sau.' });
                }
            } catch (demoError) {
                console.error('Error loading demo faces:', demoError);
                await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể tải ảnh nhận diện.' });
            }
        }
    };

    const loadMediaDataRef = useRef<number | null>(null);
    
    const loadEventMedia = async (page: number = 1, reset: boolean = false) => {
        if (isLoading || (!hasMore && page > 1)) return;
        
        // Clear previous timeout
        if (loadMediaDataRef.current) {
            clearTimeout(loadMediaDataRef.current);
        }
        
        // Debounce API call
        loadMediaDataRef.current = window.setTimeout(async () => {
            console.log(`[Frontend] Loading event media - Page: ${page}, Reset: ${reset}`);
            setIsLoading(true);
            try {
                // Call real API with headers
                const headers = commonFunc.generateApiHeaders();
                const res = await fetch(`${API_ENDPOINTS.mediaEvent}/${EVENT_ID}?page=${page}&per_page=20`, {
                    headers: headers
                });
                const data = await res.json();
                
                if (data.status === true) {
                    const newItems = data.data.items || data.data.data || [];
                    setEventMediaData(prev => reset ? newItems : [...prev, ...newItems]);
                    setCurrentPage(data.data.current_page || data.data.currentPage || page);
                    setHasMore((data.data.current_page || data.data.currentPage || page) < (data.data.last_page || data.data.totalPages || 1));
                } else {
                    console.log('Error loading event media:', data);
                    await Swal.fire({ icon: 'info', title: 'Không có thêm dữ liệu', text: data.message || 'Hết dữ liệu ảnh sự kiện.' });
                }
            } catch (error) {
                console.error('Error loading event media:', error);
                // Fallback to demo API on error
                await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể tải ảnh sự kiện.' });
            } finally {
                setIsLoading(false);
            }
        }, 300); // Debounce 300ms
    };

    const loadMyMedia = async (page: number = 1, reset: boolean = false) => {
        if (isLoading || (!hasMore && page > 1) || !session?.user.accessToken) return;
        
        console.log(`[Frontend] Loading my media - Page: ${page}, Reset: ${reset}`);
        setIsLoading(true);
        try {
            // Call real API với authorization
            const res = await fetch(`${API_ENDPOINTS.mediaUser}/${EVENT_ID}?page=${page}&per_page=10`, {
                headers: {
                    'authorization': `Bearer ${session?.user.accessToken}`
                }
            });
            const data = await res.json();
            
            if (data.status === true) {
                const newItems = data.data.items || [];
                setMyMediaData(prev => reset ? newItems : [...prev, ...newItems]);
                setCurrentPage(data.data.current_page || page);
                setHasMore((data.data.current_page || page) < (data.data.last_page || 1));
            } else {
                console.log('Error loading my media:', data);
                await Swal.fire({ icon: 'info', title: 'Không có thêm ảnh', text: data.message || 'Hết dữ liệu ảnh của bạn.' });
            }
        } catch (error) {
            console.error('Error loading my media:', error);
            await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể tải ảnh của bạn.' });
        } finally {
            setIsLoading(false);
        }
    };

    // Load my media when faces change (chỉ chạy 1 lần khi có faces)
    useEffect(() => {
        if (session && faces.length > 0 && !hasLoadedInitial) {
            setHasLoadedInitial(true);
            loadMyMedia(1, true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [faces.length, session]); // Thêm session để check authentication

    // Handle tab change
    const handleTabChange = (tab: 'event' | 'my') => {
        setActiveTab(tab);
        setCurrentPage(1);
        setHasMore(true);
        
        if (tab === 'event') {
            // Load event media if not loaded or reset
            loadEventMedia(1, true);
        } else if (tab === 'my' && session && faces.length > 0) {
            // Load my media if has faces and user is logged in
            loadMyMedia(1, true);
        }
    };

    const handleInfiniteScroll = useCallback(() => {
        if (!galleryRef.current || isLoading || !hasMore) {
            console.log('Scroll blocked:', { 
                hasGallery: !!galleryRef.current, 
                isLoading, 
                hasMore, 
                currentPage 
            });
            return;
        }
        
        const { scrollTop, scrollHeight, clientHeight } = galleryRef.current;
        const scrollThreshold = scrollHeight - clientHeight - 100; // 100px từ bottom
        
        console.log('Scroll debug:', {
            scrollTop,
            scrollHeight,
            clientHeight,
            scrollThreshold,
            isNearBottom: scrollTop >= scrollThreshold,
            activeTab,
            currentPage
        });
        
        if (scrollTop >= scrollThreshold) {
            console.log('Loading next page...');
            if (activeTab === 'event') {
                loadEventMedia(currentPage + 1);
            } else if (activeTab === 'my' && session) {
                loadMyMedia(currentPage + 1);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentPage, activeTab, session, isLoading, hasMore]); // Thêm dependencies

    useEffect(() => {
        const gallery = galleryRef.current;
        if (gallery) {
            gallery.addEventListener('scroll', handleInfiniteScroll);
            return () => gallery.removeEventListener('scroll', handleInfiniteScroll);
        }
    }, [handleInfiniteScroll]); // Listen cho tất cả tabs, không chỉ khi có faces

    const handleFileSelection = async (files: FileList) => {
        console.log('handleFileSelection called with:', files.length, 'files');
        const fileArray = Array.from(files);
        const remainingSlots = MAX_PHOTOS - faces.length - selectedFiles.length;
        
        console.log('Face slots:', { 
            MAX_PHOTOS, 
            facesLength: faces.length, 
            selectedFilesLength: selectedFiles.length, 
            remainingSlots 
        });
        
        if (fileArray.length > remainingSlots) {
            await Swal.fire({ icon: 'warning', title: 'Vượt quá giới hạn', text: `Bạn chỉ có thể chọn thêm ${remainingSlots} ảnh nữa.` });
            return;
        }

        const newFiles = [...selectedFiles, ...fileArray];
        console.log('Setting new files:', newFiles.length);
        setSelectedFiles(newFiles);

        // Create preview images
        Promise.all(
            fileArray.map(file => {
                return new Promise<string>((resolve) => {
                        const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target?.result as string);
                        reader.readAsDataURL(file);
                });
                    })
        ).then(newPreviews => {
            setPreviewImages(prev => [...prev, ...newPreviews]);
        });
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        console.log('File input changed:', e.target.files?.length);
        if (!e.target.files) return;
        handleFileSelection(e.target.files);
    };

    const removeSelectedFile = (index: number) => {
        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
        setPreviewImages(prev => prev.filter((_, i) => i !== index));
    };



    const uploadFaces = async () => {
        if (!session?.user.accessToken) return;
        if (selectedFiles.length === 0) {
            await Swal.fire({ icon: 'info', title: 'Chưa có ảnh', text: 'Vui lòng chọn ít nhất 1 ảnh để upload.' });
            return;
        }

        setIsUploadingFace(true);
        try {
            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });

            // Call real API
            const res = await fetch(API_ENDPOINTS.faceUpload, {
                method: 'POST',
                headers: {
                    'authorization': `Bearer ${session?.user.accessToken}`
                },
                body: formData
            });

            const data = await res.json();
            console.log('Upload response data:', data);
            if (data.status === 1) {
                await Swal.fire({ icon: 'success', title: 'Thành công', text: 'Upload ảnh nhận diện thành công!' });
                // Reset states
                setSelectedFiles([]);
                setPreviewImages([]);
                setIsEditingFaces(false);
                setSelectedFacesToDelete([]);
                // Reload data
                await loadFaces();
                loadMyMedia(1, true);
            } else {
                console.log('Upload failed:', data.message);
                await Swal.fire({ icon: 'error', title: 'Upload thất bại', text: data.message || 'Vui lòng thử lại.' });
            }
        } catch (error) {
            console.error('Error uploading faces:', error);
            await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể upload ảnh.' });
        } finally {
            setIsUploadingFace(false);
        }
    };

    const deleteFaces = async () => {
        if (selectedFacesToDelete.length === 0 || !session?.user.accessToken) return;

        console.log('Deleting faces:', selectedFacesToDelete);
        setIsUploadingFace(true);
        try {
            const formData = new FormData();
            selectedFacesToDelete.forEach(faceId => {
                formData.append('faceids[]', faceId);
            });

            const res = await fetch(API_ENDPOINTS.faceRemove, {
                method: 'POST',
                headers: {
                    'authorization': `Bearer ${session?.user.accessToken}`
                },
                body: formData
            });

            console.log('Delete response status:', res.status);
            const data = await res.json();
            console.log('Delete response data:', data);
            
            if (data.status === 1) {
                await Swal.fire({ icon: 'success', title: 'Đã xóa', text: 'Xóa ảnh thành công!' });
                setSelectedFacesToDelete([]);
                setIsEditingFaces(false);
                await loadFaces();
                loadMyMedia(1, true);
            } else {
                await Swal.fire({ icon: 'error', title: 'Xóa thất bại', text: data.message || 'Vui lòng thử lại.' });
            }
        } catch (error) {
            console.error('Error deleting faces:', error);
            await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Có lỗi xảy ra khi xóa.' });
        } finally {
            setIsUploadingFace(false);
        }
    };

    const toggleEditMode = () => {
        setIsEditingFaces(!isEditingFaces);
        setSelectedFacesToDelete([]);
        setSelectedFiles([]);
        setPreviewImages([]);
    };

    const toggleFaceSelection = (faceId: string) => {
        setSelectedFacesToDelete(prev => 
            prev.includes(faceId) 
                ? prev.filter(id => id !== faceId)
                : [...prev, faceId]
        );
    };

    const saveFaceChanges = async () => {
        // Kiểm tra có thay đổi nào không
        if (selectedFacesToDelete.length === 0 && selectedFiles.length === 0) {
            await Swal.fire({ icon: 'info', title: 'Chưa có thay đổi', text: 'Vui lòng chọn ảnh để xóa hoặc thêm ảnh mới!' });
            return;
        }

        // Xóa faces đã chọn trước
        if (selectedFacesToDelete.length > 0) {
            await deleteFaces();
        }
        
        // Upload faces mới
        if (selectedFiles.length > 0) {
            await uploadFaces();
        } 
        
        // Reset edit mode (sẽ được reset trong uploadFaces/deleteFaces)
        if (selectedFacesToDelete.length === 0 && selectedFiles.length === 0) {
            setIsEditingFaces(false);
        }
    };

    // Re-bind Fancybox when media data changes
    useEffect(() => {
        Fancybox.destroy();
        Fancybox.bind("[data-fancybox]");
    }, [eventMediaData, myMediaData]);

    return (
        <>
        <Header />
        <div className="page page_my_media page-no-bg-footer">
            <main className="page-main">
                <div className="container">
                    <h2 style={{margin: 0, textAlign: 'center', color: '#292828', marginTop: '24px'}}>Thư viện ảnh</h2>
                        {/* Tabs Navigation */}
                        <ul className="nav nav-tabs border-bottom-0 nav-tabs-photo" role="tablist">
                            <li className="nav-item" role="presentation">
                                <a 
                                    className={`nav-link border-0 ${activeTab === 'event' ? 'active' : ''}`}
                                    href="#"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        handleTabChange('event');
                                    }}
                                    id="event-photo"
                                >
                                    Ảnh sự kiện
                                </a>
                            </li>
                            <li className="nav-item" role="presentation">
                                <a 
                                    className={`nav-link border-0 ${activeTab === 'my' ? 'active' : ''}`}
                                    href="#"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        handleTabChange('my');
                                    }}
                                    id="my-photo"
                                >
                                    Ảnh của tôi
                                </a>
                            </li>
                        </ul>

                        <div className="tab-content" id="myTabContent">
                            {/* Event Media Tab */}
                            {activeTab === 'event' && (
                                <div className="tab-pane fade show active" id="event-photo-tab" role="tabpanel">
                                    {eventMediaData.length === 0 && !isLoading ? (
                                        <div className="empty-state">
                                            <div className="empty-state-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80" fill="none">
                                                    <circle cx="40" cy="40" r="35" stroke="#E5E7EB" strokeWidth="2" fill="#F9FAFB"/>
                                                    <path d="M25 55L35 45L40 50L50 35L55 40V55H25Z" fill="#D1D5DB"/>
                                                    <circle cx="32" cy="32" r="3" fill="#D1D5DB"/>
                                                </svg>
                                            </div>
                                            <h3 className="empty-state-title">Ảnh sự kiện hiện đang chưa có</h3>
                                            <p className="empty-state-description">
                                                Ảnh và video của sự kiện sẽ được cập nhật sớm nhất có thể
                                            </p>
                                        </div>
                                    ) : (
                                        <section className="gallery">
                                            <div 
                                                className="gallery-grid"
                                                ref={galleryRef}
                                                style={{ maxHeight: '70vh', overflowY: 'auto' }}
                                            >
                                                                                        {eventMediaData.map((item) => (
                                                <a 
                                                    key={item.id}
                                                    className="gallery-grid-item thumbnail"
                                                    data-fancybox="event-gallery"
                                                    data-src={item.url}
                                                    data-thumb={item.thumb || item.url}
                                                    data-caption={`Ảnh sự kiện #${item.id}`}
                                                    href={item.url}
                                                    style={{ cursor: 'pointer' }}
                                                >
                                                    <img className="img-fluid w-100" src={item.thumb || item.url} alt="event thumbnail" />
                                                    {item.type === 1 && (
                                                        <span className="gallery-grid-duration">
                                                            <svg className="iconsvg-play" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M12.86 5.999L6.42 2.306a2.28 2.28 0 00-3.42 2v7.413a2.28 2.28 0 003.42 1.973L12.86 10a2.28 2.28 0 000-3.947V6zm-.666 2.793l-6.44 3.747a.96.96 0 01-.947 0 .947.947 0 01-.474-.82v-7.44a.947.947 0 01.947-.947c.166.004.328.047.473.127l6.44 3.72a.947.947 0 010 1.64v-.027z"/>
                                                            </svg>
                                                            {item.duration || ''}
                                                        </span>
                                                    )}
                                                </a>
                                            ))}
                                            </div>
                                            
                                            {/* Loading indicator */}
                                            {isLoading && (
                                                <div className="page-load-status">
                                                    <div className="loading-spinner"></div>
                                                    <div className="loading-text">Đang tải thêm ảnh sự kiện...</div>
                                                </div>
                                            )}
                                        </section>
                                    )}
                                </div>
                            )}

                            {/* My Media Tab */}
                                                    {activeTab === 'my' && (
                                <div className="tab-pane fade show active" id="my-photo-tab" role="tabpanel">
                                    {/* Authentication Check */}
                                    {!session ? (
                                        /* Not logged in - Show login prompt */
                                        <div className="auth-prompt">
                                            <div className="empty-state">
                                            <div className="empty-state-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
                                                    <circle cx="32" cy="32" r="30" stroke="#007bff" strokeWidth="2" fill="none"/>
                                                    <path d="M32 20C27.58 20 24 23.58 24 28S27.58 36 32 36S40 32.42 40 28S36.42 20 32 20ZM32 34C28.69 34 26 31.31 26 28S28.69 22 32 22S38 24.69 38 28S35.31 34 32 34Z" fill="#007bff"/>
                                                    <path d="M20 52C20 46.48 24.48 42 30 42H34C39.52 42 44 46.48 44 52V54H20V52Z" fill="#007bff"/>
                                                </svg>
                                            </div>
                                            <h3 className="empty-state-title">Cần đăng nhập</h3>
                                            <p className="empty-state-description">
                                                Vui lòng đăng nhập để tìm kiếm ảnh của bạn trong sự kiện
                                            </p>
                                            <div className="empty-state-cta">
                                                <a href="#" className="btn btn-primary">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                                    </svg>
                                                    Đăng nhập ngay
                                                </a>
                                            </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {/* No Face State */}
                                            <div className={`face-upload-section ${faces.length > 0 || selectedFiles.length > 0 ? 'd-none' : ''}`}>
                                        <div className="face-upload-container">
                                            <div className="face-upload-icon">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64" fill="none">
                                                    <circle cx="32" cy="32" r="30" stroke="url(#paint0_linear)" strokeWidth="2" fill="url(#paint1_linear)"/>
                                                    <path d="M32 20C27.58 20 24 23.58 24 28S27.58 36 32 36S40 32.42 40 28S36.42 20 32 20ZM32 34C28.69 34 26 31.31 26 28S28.69 22 32 22S38 24.69 38 28S35.31 34 32 34Z" fill="url(#paint0_linear)"/>
                                                    <path d="M20 52C20 46.48 24.48 42 30 42H34C39.52 42 44 46.48 44 52V54H20V52Z" fill="white" stroke="url(#paint0_linear)"/>
                                                    <defs>
                                                        <linearGradient id="paint0_linear" x1="2" y1="2" x2="62" y2="62" gradientUnits="userSpaceOnUse">
                                                            <stop stopColor="#FCC14B"/>
                                                            <stop offset="0.5" stopColor="#A2DD99"/>
                                                            <stop offset="1" stopColor="#92E7F1"/>
                                                        </linearGradient>
                                                        <linearGradient id="paint1_linear" x1="2" y1="2" x2="62" y2="62" gradientUnits="userSpaceOnUse">
                                                            <stop stopColor="#FCC14B" stopOpacity="0.1"/>
                                                            <stop offset="0.5" stopColor="#A2DD99" stopOpacity="0.1"/>
                                                            <stop offset="1" stopColor="#92E7F1" stopOpacity="0.1"/>
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                            </div>
                                            <h3 className="face-upload-title">Nhận ảnh của riêng bạn</h3>
                                            <p className="face-upload-description">
                                                Tải lên tối đa 5 ảnh khuôn mặt của bạn để chúng tôi có thể tìm và gửi cho bạn những bức ảnh có mặt bạn trong sự kiện
                                            </p>
                                            <div className="face-upload-buttons">
                                                <button 
                                                    className="btn btn-gradient-primary"
                                                    onClick={() => {
                                                        console.log('Camera button clicked');
                                                        cameraInputRef.current?.click();
                                                    }}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path d="M10 1C5.58172 1 2 4.58172 2 9C2 13.4183 5.58172 17 10 17C14.4183 17 18 13.4183 18 9C18 4.58172 14.4183 1 10 1ZM10 15C6.68629 15 4 12.3137 4 9C4 5.68629 6.68629 3 10 3C13.3137 3 16 5.68629 16 9C16 12.3137 13.3137 15 10 15Z" fill="currentColor"/>
                                                        <circle cx="10" cy="9" r="3" fill="currentColor"/>
                                                    </svg>
                                                    Chụp ảnh
                                                </button>
                                                <button 
                                                    className="btn btn-gradient-secondary"
                                                    onClick={() => {
                                                        console.log('Gallery button clicked');
                                                        fileInputRef.current?.click();
                                                    }}
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path d="M3 3C2.44772 3 2 3.44772 2 4V16C2 16.5523 2.44772 17 3 17H17C17.5523 17 18 16.5523 18 16V4C18 3.44772 17.5523 3 17 3H3ZM4 5H16V15H4V5Z" fill="currentColor"/>
                                                        <path d="M6 7L10 11L14 7H6Z" fill="currentColor"/>
                                                    </svg>
                                                    Chọn từ thư viện
                                                </button>
                                            </div>
                        </div>
                    </div>

                                {/* Has Face State */}
                                <div className={`face-management-section ${faces.length === 0 && selectedFiles.length === 0 ? 'd-none' : ''}`}>
                                    <div className="face-management-header">
                                        <div className="face-count-badge">
                                            <span className="face-count-number">{faces.length + selectedFiles.length}</span>
                                            <span className="face-count-total">/5</span>
                                            <span className="face-count-label">Ảnh nhận diện</span>
                                        </div>
                                        {!isEditingFaces ? (
                                            <button 
                                                className="btn-edit-faces"
                                                onClick={toggleEditMode}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                    <path d="M11.013 1.427a1.75 1.75 0 0 1 2.474 0l1.086 1.086a1.75 1.75 0 0 1 0 2.474L8.224 11.336a4.75 4.75 0 0 1-2.124 1.262L3.5 13.5l.902-2.6a4.75 4.75 0 0 1 1.262-2.124L11.013 1.427Z"/>
                                                </svg>
                                                Chỉnh sửa
                                            </button>
                                        ) : (
                                            <div className="edit-buttons">
                                                <button 
                                                    className="btn-cancel-edit"
                                                    onClick={toggleEditMode}
                                                >
                                                    Hủy
                                                </button>
                                                <button 
                                                    className="btn-save-edit"
                                                    onClick={saveFaceChanges}
                                                    disabled={isUploadingFace}
                                                >
                                                    {isUploadingFace ? 'Đang lưu...' : 
                                                        `Lưu${selectedFacesToDelete.length > 0 || selectedFiles.length > 0 ? 
                                                            ` (${selectedFacesToDelete.length > 0 ? `xóa ${selectedFacesToDelete.length}` : ''}${selectedFacesToDelete.length > 0 && selectedFiles.length > 0 ? ', ' : ''}${selectedFiles.length > 0 ? `thêm ${selectedFiles.length}` : ''})` : 
                                                            ''}`
                                                    }
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                    
                                    {/* Face Gallery */}
                                    <div id="list-face-wrap" className="mb-5 gallery-grid-2">
                                        {faces.map((face) => (
                                            <div 
                                                key={face.face_id} 
                                                className={`gallery-grid-item thumbnail ${isEditingFaces && selectedFacesToDelete.includes(face.face_id) ? 'selected-for-delete' : ''}`} 
                                                style={{ position: 'relative' }}
                                            >
                                                <img className="img-fluid w-100" src={face.url} alt="thumbnail" />
                                                {isEditingFaces && (
                                                    <button 
                                                        className="badge-delete"
                                                        onClick={() => toggleFaceSelection(face.face_id)}
                                                        style={{
                                                            position: 'absolute',
                                                            top: 8,
                                                            right: 8,
                                                            background: selectedFacesToDelete.includes(face.face_id) 
                                                                ? 'rgba(220, 53, 69, 0.9)' 
                                                                : 'rgba(0, 0, 0, 0.6)',
                                                            color: '#fff',
                                                            border: selectedFacesToDelete.includes(face.face_id) 
                                                                ? '2px solid #dc3545' 
                                                                : 'none',
                                                            borderRadius: '50%',
                                                            width: 32,
                                                            height: 32,
                                                            cursor: 'pointer',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            fontSize: '18px',
                                                            fontWeight: 'bold',
                                                            transition: 'all 0.2s ease'
                                                        }}
                                                        title={selectedFacesToDelete.includes(face.face_id) ? "Bỏ chọn" : "Chọn để xóa"}
                                                    >
                                                        {selectedFacesToDelete.includes(face.face_id) ? '✓' : '×'}
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        
                                        {/* Preview selected files */}
                                        {previewImages.map((src, idx) => (
                                            <div key={`preview-${idx}`} className="gallery-grid-item thumbnail" style={{ position: 'relative' }}>
                                                <img className="img-fluid w-100" src={src} alt={`Preview ${idx + 1}`} />
                                <button
                                                    className="badge-delete"
                                                    onClick={() => removeSelectedFile(idx)}
                                    style={{
                                        position: 'absolute',
                                        top: 8,
                                        right: 8,
                                                        background: 'rgba(220, 53, 69, 0.8)',
                                        color: '#fff',
                                        border: 'none',
                                        borderRadius: '50%',
                                        width: 32,
                                        height: 32,
                                        cursor: 'pointer',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                                        fontSize: '18px',
                                                        fontWeight: 'bold'
                                    }}
                                    title="Xóa ảnh"
                                >
                                    ×
                                </button>
                            </div>
                        ))}
                                        
                                        {/* Empty slots */}
                                        {Array.from({ length: MAX_PHOTOS - faces.length - selectedFiles.length }).map((_, idx) => (
                            <div
                                                key={`empty-${idx}`}
                                className="gallery-grid-item photo-select"
                                                onClick={() => fileInputRef.current?.click()}
                                                style={{ cursor: 'pointer' }}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="33" height="33" viewBox="0 0 33 33" fill="none">
                                    <g clipPath="url(#clip0_6766_14806)">
                                                        <path d="M28.4998 20.5V24.5H32.4998V27.1667H28.4998V31.1667H25.8332V27.1667H21.8332V24.5H25.8332V20.5H28.4998ZM28.5108 4.5C29.2412 4.5 29.8332 5.09327 29.8332 5.82453V17.8333H27.1665V7.16667H5.83317V25.832L19.1665 12.5L23.1665 16.5V20.272L19.1665 16.2712L9.6025 25.8333H19.1665V28.5H4.4889C3.75857 28.5 3.1665 27.9068 3.1665 27.1755V5.82453C3.1665 5.09301 3.77358 4.5 4.4889 4.5H28.5108ZM11.1665 9.83333C12.6393 9.83333 13.8332 11.0272 13.8332 12.5C13.8332 13.9728 12.6393 15.1667 11.1665 15.1667C9.69374 15.1667 8.49984 13.9728 8.49984 12.5C8.49984 11.0272 9.69374 9.83333 11.1665 9.83333Z" fill="white"/>
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_6766_14806">
                                                            <rect width="32" height="32" fill="white" transform="translate(0.5 0.5)"/>
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                        ))}
                    </div>

                                    {/* Edit mode actions */}
                                    {isEditingFaces && (
                                        <div className="d-flex btn-photo mb-4">
                                            <button 
                                                className="btn btn-gradient-border w-50 me-2"
                                                onClick={() => cameraInputRef.current?.click()}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                    <path d="M6.09621 6.58368L6.10923 6.58436C6.18192 6.58815 6.25385 6.56805 6.31404 6.52712C6.37389 6.48642 6.41886 6.42736 6.44217 6.35887L6.89159 4.99397L6.89278 4.99034L6.89279 4.99035C6.98224 4.72552 7.15266 4.4955 7.37996 4.3328C7.60706 4.17024 7.87947 4.08311 8.15874 4.08368M6.09621 6.58368L8.15946 4.08368C8.15922 4.08368 8.15898 4.08368 8.15874 4.08368M6.09621 6.58368H6.08317H4.4165C4.06288 6.58368 3.72374 6.72415 3.4737 6.9742C3.22365 7.22425 3.08317 7.56339 3.08317 7.91701V14.5837C3.08317 14.9373 3.22365 15.2764 3.47369 15.5265C3.72374 15.7765 4.06288 15.917 4.4165 15.917H16.0832C16.4368 15.917 16.7759 15.7765 17.026 15.5265C17.276 15.2764 17.4165 14.9373 17.4165 14.5837V7.91701C17.4165 7.56339 17.276 7.22425 17.026 6.9742C16.7759 6.72415 16.4368 6.58368 16.0832 6.58368H14.4312C14.3675 6.57885 14.3064 6.55583 14.2554 6.51725C14.2022 6.47706 14.1623 6.42185 14.1408 6.35876C14.1407 6.35837 14.1405 6.35798 14.1404 6.35758L13.6914 4.99397L13.6914 4.99396L13.6902 4.99035C13.6008 4.72552 13.4303 4.4955 13.2031 4.3328C12.9759 4.17024 12.7035 4.08311 12.4243 4.08368M6.09621 6.58368L12.4243 4.08368M8.15874 4.08368H12.4243M8.15874 4.08368H12.4243M16.0832 5.91701H16.0832C16.3487 5.91697 16.6115 5.96979 16.8564 6.07238C17.1012 6.17496 17.3232 6.32527 17.5094 6.51453C17.6955 6.70379 17.8421 6.92822 17.9407 7.17474C18.0392 7.42125 18.0877 7.68491 18.0832 7.95034L18.0832 7.95034V7.95868V14.6253C18.0832 15.1558 17.8725 15.6645 17.4974 16.0396C17.1223 16.4146 16.6136 16.6253 16.0832 16.6253H4.4165C3.88607 16.6253 3.37736 16.4146 3.00229 16.0396C2.62722 15.6645 2.4165 15.1558 2.4165 14.6253V7.95868C2.4165 7.42825 2.62722 6.91954 3.00229 6.54446C3.37736 6.16939 3.88607 5.95868 4.4165 5.95868H5.48317H5.84815L5.95938 5.61107L6.22505 4.78086C6.35798 4.38464 6.61171 4.03998 6.95062 3.79535C7.29021 3.55022 7.69818 3.41789 8.11699 3.41701L12.3832 3.41701L12.3851 3.41701C12.8 3.41541 13.2051 3.54289 13.5444 3.78178C13.8817 4.01931 14.1372 4.35529 14.276 4.74363L14.5403 5.5694L14.6515 5.91701H15.0165H16.0832ZM8.67572 8.06118C9.14166 7.74985 9.68946 7.58368 10.2498 7.58368C11.0013 7.58368 11.722 7.88219 12.2533 8.41354C12.7847 8.94489 13.0832 9.66556 13.0832 10.417C13.0832 10.9774 12.917 11.5252 12.6057 11.9911C12.2943 12.4571 11.8518 12.8202 11.3341 13.0347C10.8164 13.2491 10.2467 13.3052 9.69708 13.1959C9.14747 13.0866 8.64262 12.8167 8.24637 12.4205C7.85012 12.0242 7.58027 11.5194 7.47095 10.9698C7.36162 10.4202 7.41773 9.85046 7.63218 9.33274C7.84663 8.81502 8.20978 8.37251 8.67572 8.06118ZM9.0461 12.2185C9.40241 12.4566 9.82131 12.5837 10.2498 12.5837C10.8245 12.5837 11.3756 12.3554 11.7819 11.9491C12.1882 11.5427 12.4165 10.9916 12.4165 10.417C12.4165 9.98848 12.2894 9.56958 12.0514 9.21328C11.8133 8.85697 11.4749 8.57926 11.079 8.41527C10.6831 8.25128 10.2474 8.20837 9.82714 8.29198C9.40685 8.37558 9.02079 8.58193 8.71777 8.88495C8.41476 9.18796 8.2084 9.57402 8.1248 9.99432C8.0412 10.4146 8.08411 10.8503 8.2481 11.2462C8.41209 11.6421 8.6898 11.9805 9.0461 12.2185Z" fill="url(#paint0_linear_7503_11439)" stroke="url(#paint1_linear_7503_11439)"/>
                                                </svg>
                                                Chụp ảnh
                                            </button>
                                            <button 
                                                className="btn btn-gradient-border w-50"
                                                onClick={() => fileInputRef.current?.click()}
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                    <circle cx="13.5832" cy="6.66667" r="1.66667" stroke="url(#paint0_linear_7503_11442)" strokeWidth="1.5"/>
                                                    <path d="M1.9165 10.4165L3.37616 9.13927C4.13555 8.47481 5.28007 8.51292 5.99357 9.22642L9.56835 12.8012C10.141 13.3739 11.0425 13.452 11.7052 12.9863L11.9537 12.8116C12.9072 12.1415 14.1973 12.2192 15.0636 12.9988L17.7498 15.4165" stroke="url(#paint1_linear_7503_11442)" strokeWidth="1.5" strokeLinecap="round"/>
                                                </svg>
                                                Thư viện
                                            </button>
                                        </div>
                                    )}

                                    {/* Upload button khi có selectedFiles (không trong edit mode) */}
                                    {!isEditingFaces && selectedFiles.length > 0 && (
                                        <div className="mt-4 d-flex justify-content-center">
                                            <button 
                                                className="btn btn-gradient-primary"
                                                onClick={uploadFaces}
                                                disabled={isUploadingFace}
                                            >
                                                {isUploadingFace ? 'Đang upload...' : `Upload ${selectedFiles.length} ảnh`}
                                            </button>
                                        </div>
                                    )}

                                </div>

                                    {/* My Media Gallery */}
                                    {faces.length > 0 && (
                                        <div className="my-media-section">
                                            <div className="my-media-header">
                                                <h4 className="my-media-title">Ảnh của tôi trong sự kiện</h4>
                                                <p className="my-media-subtitle">
                                                    Những bức ảnh và video có mặt bạn trong sự kiện
                                                </p>
                                            </div>
                                            
                                            {myMediaData.length === 0 ? (
                                                <div className="empty-state my-media-empty">
                                                    <div className="empty-state-icon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                                            <circle cx="30" cy="30" r="25" stroke="#E5E7EB" strokeWidth="2" fill="#F9FAFB"/>
                                                            <path d="M20 40L25 35L30 40L40 25L45 30V40H20Z" fill="#D1D5DB"/>
                                                            <circle cx="25" cy="25" r="2" fill="#D1D5DB"/>
                                                            <path d="M35 15C37.2091 15 39 16.7909 39 19C39 21.2091 37.2091 23 35 23C32.7909 23 31 21.2091 31 19C31 16.7909 32.7909 15 35 15Z" fill="#FCC14B"/>
                                                        </svg>
                                                    </div>
                                                    <h5 className="empty-state-title">Đang tìm kiếm ảnh của bạn</h5>
                                                    <p className="empty-state-description">
                                                        Cảm ơn bạn! Ảnh có mặt bạn trong sự kiện sẽ được cập nhật sớm nhất có thể
                                                    </p>
                                                </div>
                                            ) : (
                                                <section className="gallery my-media-gallery">
                                                    <div 
                                                        className="gallery-grid"
                                                        ref={galleryRef}
                                                        style={{ maxHeight: '50vh', overflowY: 'auto' }}
                                                    >
                                                        {myMediaData.map((item) => (
                                                            <a 
                                                                key={item.id}
                                                                className="gallery-grid-item thumbnail"
                                                                data-fancybox="my-gallery"
                                                                data-src={item.url}
                                                                data-thumb={item.thumb || item.url}
                                                                data-caption={`Ảnh của tôi #${item.id}`}
                                                                href={item.url}
                                                                style={{ cursor: 'pointer' }}
                                                            >
                                                                <img className="img-fluid w-100" src={item.thumb || item.url} alt="my media thumbnail" />
                                                                {item.type === 1 && (
                                                                    <span className="gallery-grid-duration">
                                                                        <svg className="iconsvg-play" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M12.86 5.999L6.42 2.306a2.28 2.28 0 00-3.42 2v7.413a2.28 2.28 0 003.42 1.973L12.86 10a2.28 2.28 0 000-3.947V6zm-.666 2.793l-6.44 3.747a.96.96 0 01-.947 0 .947.947 0 01-.474-.82v-7.44a.947.947 0 01.947-.947c.166.004.328.047.473.127l6.44 3.72a.947.947 0 010 1.64v-.027z"/>
                                                                        </svg>
                                                                        {item.duration || ''}
                                                                    </span>
                                                                )}
                                                            </a>
                                                        ))}
                                                    </div>
                                                    
                                                    {/* Loading indicator */}
                                                    {isLoading && (
                                                        <div className="page-load-status">
                                                            <div className="loading-spinner"></div>
                                                            <div className="loading-text">Đang tải ảnh của bạn...</div>
                                                        </div>
                                                    )}
                                                </section>
                                            )}
                                        </div>
                                    )}
                                </>
                            )}
                                </div>
                            )}
                    </div>
                    </div>

                    {/* Hidden file inputs */}
                        <input
                            type="file"
                            accept="image/*"
                            multiple
                            ref={fileInputRef}
                            style={{ display: "none" }}
                            onChange={handleFileChange}
                    />
                    <input
                        type="file"
                        accept="image/*"
                        multiple
                        capture="environment"
                        ref={cameraInputRef}
                        style={{ display: "none" }}
                        onChange={handleFileChange}
                    />

                    
            </main>
        </div>
        </>
    );
}

export default Page
