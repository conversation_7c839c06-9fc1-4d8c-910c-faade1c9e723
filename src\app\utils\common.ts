import crypto from 'crypto';

const generateApiHeaders = (body = {}) => {
    // IMPORTANT: These environment variables must be available on the client-side.
    // In Next.js, they should be prefixed with NEXT_PUBLIC_.
    const apiKey = process.env.NEXT_PUBLIC_API_KEY || 'frontend-public-key-5e2d3f7b-dc45-4a88-b05c-8ac9a7a9e7b1';
    const apiSecret = process.env.NEXT_PUBLIC_API_SECRET || 'd25c7bfa83de4a79b957e7cd99213f3887b91f62893de15f99ef4bd719bd8c2a';

    if (!apiKey || !apiSecret) {
        // Instead of returning an empty object, throw an error to be caught by the calling function.
        // This ensures that the function signature is consistent and avoids type errors.
        throw new Error("API Key or Secret is not defined in environment variables.");
    }

    const timestamp = Math.floor(Date.now() / 1000).toString();
    const raw = `${timestamp}.${JSON.stringify(body)}`;
    const signature = crypto.createHmac('sha256', apiSecret).update(raw).digest('hex');

    return {
        'x-api-key': apiKey,
        'x-signature': signature,
        'x-timestamp': timestamp,
    };
};

const commonFunc = {
    generateApiHeaders
}

export default commonFunc
