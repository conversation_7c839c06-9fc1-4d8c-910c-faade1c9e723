'use client';

import { useState, useEffect } from 'react';
import { API_ENDPOINTS } from '../utils/api-endpoints';
import commonFunc from '../utils/common';

interface Product {
    name: string;
    image: string;
    link: string | null;
    brand_name: string | null;
    brand_logo: string | null;
}

const Product = () => {
    const [products, setProducts] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);

    const fetchProducts = async (pageNum: number, append: boolean = false) => {
        append ? setIsLoadingMore(true) : setIsLoading(true);
        try {
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(`${API_ENDPOINTS.productFeatured}?page=${pageNum}`, { headers });
            const data = await res.json();
            console.log('data fetchProducts', data);
            if (data.status) {
                const items: Product[] = data?.data?.data || [];
                setProducts(prev => append ? [...prev, ...items] : items);
                const current = Number(data?.data?.page ?? pageNum);
                const total = Number(data?.data?.total ?? items.length);
                const limitValue = data?.data?.limit;
                const limit = Number(limitValue != null ? limitValue : (items.length || 1));
                const last = limit > 0 ? Math.ceil(total / limit) : current;
                setTotalPages(last);
                setPage(current);
            } else {
                setError(data.message);
            }
        } catch {
            setError('Failed to fetch products. Please try again later.');
        } finally {
            append ? setIsLoadingMore(false) : setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchProducts(1, false);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleLoadMore = () => {
        if (page < totalPages && !isLoadingMore) {
            fetchProducts(page + 1, true);
        }
    };

    if (isLoading) {
        return <div className="flex justify-center items-center h-64"><div className="loader"></div><p className="ml-4 text-gray-400">Loading Products...</p></div>;
    }

    if (error) {
        return <div className="text-red-500 text-center p-4">{error}</div>;
    }

    return (
        <section>
            <div className="container container-lg-fluid">
                <div className="product-grid">
                    {products.map((product, index) => (
                        <div key={index} className="card card-product">
                            <a className="thumbnail thumbnail-hover" href={product.link || '#'}>
                                <span className="thumbnail-inner">
                                    <img className="thumbnail-img" src={product.image} alt={product.name} />
                                </span>
                            </a>
                            <div className="card-body">
                                <div className="flex-center-y gap-2">
                                    <span className="avatar">
                                        <img className="img-fluid w-100 avatar-img" src={product.brand_logo || 'images/ex/home/<USER>/logo.webp'} alt="" />
                                    </span>
                                    {product.brand_name || 'Brand'}
                                </div>
                                <h3 className="card-title mb-0">
                                    <a href={product.link || '#'}>{product.name}</a>
                                </h3>
                                <span className="card-action">
                                    <button className="btn btn-secondary btn-square rounded-circle" type="button">
                                        <svg className="iconsvg-arrow-right">
                                            <use xlinkHref="images/sprite.svg#arrow-right"></use>
                                        </svg>
                                    </button>
                                </span>
                            </div>
                        </div>
                    ))}
                </div>
                <div className="text-center py-5 my-xxl-5">
                    {page < totalPages && (
                        <button
                            className="btn btn-3d btn-primary btn-xxl-lg text-uppercase ff-secondary fw-bold btn-336px"
                            type="button"
                            onClick={handleLoadMore}
                            disabled={isLoadingMore}
                        >
                            {isLoadingMore ? 'Đang tải...' : 'Xem thêm'}
                        </button>
                    )}
                </div>
            </div>
        </section>
    );
}

export default Product;
