function fbLogin() {
    console.log('fbLogin...'); //return;
    FB.login(function (response) {
        if (response.authResponse) {
            fetchFBProfile();
        } else {
            showError('Facebook login failed');
        }
    }, { scope: 'public_profile' });
}

function fetchFBProfile() {

    console.log('Fetching Facebook profile...'); //return;

    FB.api('/me', { fields: 'name,email,picture' }, async function (response) {
        if (!response || response.error) {
            showError('Failed to fetch profile');
            return;
        }

        console.log('Facebook profile:', response);

        // Gửi accessToken về backend x<PERSON>c thực (tuỳ cấu hình <PERSON>Auth backend bạn)
        const accessToken = FB.getAuthResponse().accessToken;
        const browser = navigator.userAgent;

        try {
            const ip = await fetch('https://api.ipify.org?format=json').then(res => res.json());
            const serverResponse = await fetch('https://dev-idol14-api.net-solutions.vn/v1' + '/auth/facebook', {
                //const result = await fetch('http://localhost:8080/papi/v1/auth/facebook', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    accessToken: accessToken,
                    browser: browser,
                    ipAddress: ip.ip
                })
            });
            const data = await serverResponse.json();

            console.log('Server response:', data);

            if (!serverResponse.ok) {
                throw new Error(data.error || 'Authentication failed');
            }

            console.log('Authentication successful:', data.data.sessionToken);
            window.postMessage(
                {
                    accessToken: data.data.sessionToken,
                },
                '*'
            );

            //showProfile(response);
        } catch (err) {
            //showError(err.message);
        }
    });
}
