'use client';
import { useCallback, useState } from 'react';
import Swal from 'sweetalert2';
import { API_ENDPOINTS } from '../utils/api-endpoints';
import commonFunc from '../utils/common';
interface Product {
    name: string;
    description: string;
    link: string;
    image: string;
    monopoly: number;
    interview: number;
    // Local-only fields for client-side image handling
    file?: File | null;
    previewUrl?: string;
}

const Footer = () => {
    const [showModal, setShowModal] = useState(false);
    const [name, setName] = useState('');
    const [position, setPosition] = useState('');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');
    const [company, setCompany] = useState('');
    const [company_phone, setCompanyPhone] = useState('');
    const [link, setLink] = useState('');
    const [address, setAddress] = useState('');
    // Use string for select control; convert to number when submitting
    const [categoryId, setCategoryId] = useState<string>('');
    const [products, setProducts] = useState<Product[]>([
        { name: '', description: '', link: '', image: '', monopoly: 0, interview: 0, file: null, previewUrl: '' },
    ]);

    // Field-level validation errors
    const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
    const [productErrors, setProductErrors] = useState<Array<{ name?: string }>>([]);

    const handleAddProduct = () => {
        setProducts([
            ...products,
            { name: '', description: '', link: '', image: '', monopoly: 0, interview: 0, file: null, previewUrl: '' },
        ]);
        setProductErrors((prev) => [...prev, {}]);
    };

    const handleCategoryChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
        console.log('Selected value:', e.target.value);
        setCategoryId(e.target.value);
    }, []);

    const handleProductChange = <K extends keyof Product>(index: number, field: K, value: Product[K]) => {
        const newProducts = [...products];
        newProducts[index][field] = value;
        setProducts(newProducts);
        if (field === 'name') {
            setProductErrors((prev) => {
                const next = [...prev];
                if (!next[index]) next[index] = {};
                next[index].name = '';
                return next;
            });
        }
    };

    const handleImageChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] ?? null;
        if (!file) return;
        const newProducts = [...products];
        newProducts[index].file = file;
        newProducts[index].previewUrl = URL.createObjectURL(file);
        setProducts(newProducts);
    };

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (isSubmitting) return;
        setIsSubmitting(true);

        // --- Validation ---
        const errors: Record<string, string> = {};
        const prodErrors: Array<{ name?: string }> = products.map(() => ({}));

        const isValidEmail = (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val.trim());
        const isValidPhone = (val: string) => {
            const digits = val.replace(/\D/g, '');
            return digits.length >= 9 && digits.length <= 11; // VN phổ biến 9-11 số
        };

        if (!name.trim()) errors.name = 'Trường bắt buộc';
        if (!email.trim()) errors.email = 'Trường bắt buộc';
        else if (!isValidEmail(email)) errors.email = 'Email không hợp lệ';
        if (!phone.trim()) errors.phone = 'Trường bắt buộc';
        else if (!isValidPhone(phone)) errors.phone = 'Số điện thoại không hợp lệ';
        if (!company.trim()) errors.company = 'Trường bắt buộc';
        if (!company_phone.trim()) errors.company_phone = 'Trường bắt buộc';
        else if (!isValidPhone(company_phone)) errors.company_phone = 'Số điện thoại không hợp lệ';
        if (!link.trim()) errors.link = 'Trường bắt buộc';
        if (!address.trim()) errors.address = 'Trường bắt buộc';

        products.forEach((p, idx) => {
            if (!String(p.name || '').trim()) {
                prodErrors[idx].name = 'Trường bắt buộc';
            }
        });

        setFieldErrors(errors);
        setProductErrors(prodErrors);
        const hasFieldErrors = Object.keys(errors).length > 0;
        const hasProdErrors = prodErrors.some((pe) => pe.name);
        if (hasFieldErrors || hasProdErrors) {
            setIsSubmitting(false);
            return;
        }

        // Upload images per product (if any) and build payload products
        const uploadImage = async (file: File): Promise<string> => {
            const formData = new FormData();
            formData.append('file', file);
            const headers = commonFunc.generateApiHeaders();
            const res = await fetch(API_ENDPOINTS.brandUploadImage, {
                method: 'POST',
                headers: {
                    'x-api-key': headers['x-api-key'],
                    'x-signature': headers['x-signature'],
                    'x-timestamp': headers['x-timestamp'],
                },
                body: formData,
            });
            const data = await res.json();
            if (data.status) {
                return data.data.url as string;
            }
            throw new Error(data.message || 'Image upload failed');
        };

        let updatedProducts: Product[] = [];
        try {
            const uploadedUrls = await Promise.all(
                products.map((p) => (p.file ? uploadImage(p.file) : Promise.resolve(p.image || '')))
            );

            updatedProducts = products.map((p, idx) => ({
                name: p.name,
                description: p.description,
                link: p.link,
                image: uploadedUrls[idx] || '',
                monopoly: p.monopoly,
                interview: p.interview,
            }));
        } catch (error) {
            console.error('Error uploading one of the images:', error);
            await Swal.fire({ icon: 'error', title: 'Upload ảnh thất bại', text: 'Vui lòng kiểm tra lại ảnh và thử lại.' });
            setIsSubmitting(false);
            return;
        }

        const contactData = {
            name,
            position,
            email,
            phone,
            company,
            company_phone,
            link,
            address,
            category_id: Number(categoryId) || 0,
            products: updatedProducts,
        };

        try {
            const headers = commonFunc.generateApiHeaders(contactData);
            const res = await fetch(API_ENDPOINTS.brandContact, {
                method: 'POST',
                headers: {
                    ...headers,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData),
            });
            const data = await res.json();
            if (data.status) {
                await Swal.fire({ icon: 'success', title: 'Gửi giới thiệu thành công', text: 'Cảm ơn bạn đã gửi thông tin. Chúng tôi sẽ liên hệ sớm nhất!' });
                setShowModal(false);
                // reset form
                setName('');
                setPosition('');
                setEmail('');
                setPhone('');
                setCompany('');
                setCompanyPhone('');
                setLink('');
                setAddress('');
                setCategoryId('');
                setProducts([{ name: '', description: '', link: '', image: '', monopoly: 0, interview: 0, file: null, previewUrl: '' }]);
            } else {
                console.error('Contact submission failed:', data.message);
                await Swal.fire({ icon: 'error', title: 'Gửi giới thiệu thất bại', text: data.message || 'Vui lòng thử lại sau.' });
            }
        } catch (error) {
            console.error('Error submitting contact:', error);
            await Swal.fire({ icon: 'error', title: 'Có lỗi xảy ra', text: 'Không thể gửi thông tin. Vui lòng thử lại sau.' });
        } finally {
            setIsSubmitting(false);
        }
    };


    return (
        <>
            <footer className="footer" id="footer">
                <div className="container">
                    <h2 className="footer-title fw-normal text-uppercase text-center">
                        Doanh nghiệp giới thiệu sản phầm
                    </h2>
                    <p className="footer-subtitle rounded-1 bg-primary-dark fw-medium px-3 py-2 lh-heading mb-4">
                        Trạm tiếp sức Yêu nước
                    </p>
                    <div className="row g-2 g-sm-4 mb-4">
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                            <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Viện ngôn ngữ và văn hóa Đức</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            56-58-60, P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Mầm non Thiên Nhiên</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                            <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            40 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                            <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            67 Nguyễn Thái Học
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Cục biểu diễn Nghệ thuật</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            32 Nguyễn Thái Học
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div className="col-xxl-3 col-lg-4 col-sm-6">
                            <a className="card card-location h-100" href="#" target="_blank">
                                <div className="card-body">
                                    <h3 className="card-title pb-1">Bảo tàng Mỹ thuật Việt Nam</h3>
                                    <div className="d-flex gap-2">
                                        <svg className="iconsvg-pin icon-lg flex-shrink-0">
                                             <use xlinkHref={ "/images/sprite.svg#pin"} />
                                        </svg>
                                        <span className="fs-sm align-self-center">
                                            66 P. Nguyễn Thái Học, Điện Biên, Ba Đình, Hà Nội, Vietnam
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div className="footer-hotline ff-secondary text-center">
                        <div className="row gx-2 gy-1 align-items-center justify-content-center">
                            <div className="col-auto">
                                Để đăng ký vui lòng liên hệ HOTLINE:
                            </div>
                            <div className="col-auto">
                                <a className="footer-hotline-phone fw-semibold flex-center-y" href="tel:0886008585">
                                    <svg className="iconsvg-phone">
                                        <use xlinkHref={"/images/sprite.svg#phone" }></use>
                                    </svg>0886.00.8585
                                </a>
                            </div>
                            <div className="col-auto">
                                hoặc điền thông tin
                                <a className="fw-semibold text-uppercase" href="#" onClick={() => setShowModal(true)}>
                                    tại đây
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            {showModal && (
                <div className="modal fade modal-toplist-reg show" style={{ display: 'block' }} id="modal-toplist-reg" tabIndex={-1} aria-hidden="true">
                    <div className="modal-dialog modal-dialog-centered modal-fullscreen">
                        <div className="modal-content">
                            <div className="section section-has-bg-video flex-center py-0">
                                <video className="section-bg-video" autoPlay muted loop playsInline>
                                    <source src={"images/home/<USER>" } type="video/mp4" />Your browser does not support the video tag.
                                </video>
                                <form className="form-reg mx-auto" onSubmit={handleSubmit}>
                                    <button className="modal-btn-close btn-close" type="button" onClick={() => setShowModal(false)} aria-label="Close">
                                        <svg className="iconsvg-x">
                                            <use xlinkHref={"/images/sprite.svg#x"}></use>
                                        </svg>
                                    </button>
                                    <div className="scroll-y d-flex flex-column gap-4">
                                        <div>
                                            <h3 className="form-reg-title rounded-1 bg-primary-dark fw-medium px-3 py-2 ff-base">Thông tin liên hệ</h3>
                                            <div className="row g-12px">
                                                <div className="col-md-6">
                                                    <div className="form-floating">
                                                    <input className="form-control" type="text" aria-label="" id="form-reg-fullName" placeholder="Họ và tên" value={name} onChange={(e) => { setName(e.target.value); setFieldErrors((prev) => ({ ...prev, name: '' })); }} />
                                                        <label htmlFor="form-reg-fullName">
                                                            Họ và tên
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                {fieldErrors.name && <div className="text-danger mt-1">{fieldErrors.name}</div>}
                                                </div>
                                                <div className="col-md-6">
                                                    <div className="form-floating">
                                                        <input className="form-control" type="text" aria-label="" id="form-reg-position" placeholder="Chức vụ" value={position} onChange={(e) => setPosition(e.target.value)} />
                                                        <label htmlFor="form-reg-position">
                                                            Chức vụ
                                                        </label>
                                                    </div>
                                                </div>
                                                <div className="col-md-6">
                                                    <div className="form-floating">
                                                    <input className="form-control" type="email" aria-label="" id="form-reg-email" placeholder="Email" value={email} onChange={(e) => { setEmail(e.target.value); setFieldErrors((prev) => ({ ...prev, email: '' })); }} />
                                                        <label htmlFor="form-reg-email">
                                                            Email
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                {fieldErrors.email && <div className="text-danger mt-1">{fieldErrors.email}</div>}
                                                </div>
                                                <div className="col-md-6">
                                                    <div className="form-floating">
                                                    <input className="form-control" type="text" aria-label="" id="form-reg-phone" placeholder="Số điện thoại" value={phone} onChange={(e) => { setPhone(e.target.value); setFieldErrors((prev) => ({ ...prev, phone: '' })); }} />
                                                        <label htmlFor="form-reg-phone">
                                                            Số điện thoại
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                {fieldErrors.phone && <div className="text-danger mt-1">{fieldErrors.phone}</div>}
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h3 className="form-reg-title rounded-1 bg-primary-dark fw-medium px-3 py-2 ff-base">Thông tin Doanh nghiệp</h3>
                                            <div className="row g-12px">
                                                <div className="col-12">
                                                    <div className="form-floating">
                                                        <input className="form-control" type="text" aria-label="" id="form-reg-company-name" placeholder="Tên doanh nghiệp/hộ kinh doanh" value={company} onChange={(e) => { setCompany(e.target.value); setFieldErrors((prev) => ({ ...prev, company: '' })); }} />
                                                        <label htmlFor="form-reg-company-name">
                                                            Tên doanh nghiệp/hộ kinh doanh
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                    {fieldErrors.company && <div className="text-danger mt-1">{fieldErrors.company}</div>}
                                                </div>
                                                <div className="col-12">
                                                    <div className="form-floating">
                                                        <input className="form-control" type="text" aria-label="" id="form-reg-company-phone" placeholder="Số điện thoại công ty" value={company_phone} onChange={(e) => { setCompanyPhone(e.target.value); setFieldErrors((prev) => ({ ...prev, company_phone: '' })); }} />
                                                        <label htmlFor="form-reg-company-phone">
                                                            Số điện thoại công ty
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                    {fieldErrors.company_phone && <div className="text-danger mt-1">{fieldErrors.company_phone}</div>}
                                                </div>
                                                <div className="col-12">
                                                    <div className="form-floating">
                                                        <input className="form-control" type="text" aria-label="" id="form-reg-company-website" placeholder="Website/Fanpage công ty" value={link} onChange={(e) => { setLink(e.target.value); setFieldErrors((prev) => ({ ...prev, link: '' })); }} />
                                                        <label htmlFor="form-reg-company-website">
                                                            Website/Fanpage công ty
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                    {fieldErrors.link && <div className="text-danger mt-1">{fieldErrors.link}</div>}
                                                </div>
                                                <div className="col-12">
                                                    <div className="form-floating">
                                                        <input className="form-control" type="text" aria-label="" id="form-reg-company-address" placeholder="Địa chỉ" value={address} onChange={(e) => { setAddress(e.target.value); setFieldErrors((prev) => ({ ...prev, address: '' })); }} />
                                                        <label htmlFor="form-reg-company-address">
                                                            Địa chỉ
                                                            <span className="text-danger ff-base">*</span>
                                                        </label>
                                                    </div>
                                                    {fieldErrors.address && <div className="text-danger mt-1">{fieldErrors.address}</div>}
                                                </div>
                                                <div className="col-12">
                                                    <div className="form-floating">
                                                        <select
                                                            className={`form-select ${categoryId ? '' : 'form-select-default'}`}
                                                            aria-label=""
                                                            id="form-reg-business-field"
                                                            value={categoryId}
                                                            onChange={handleCategoryChange}
                                                        >
                                                            <option value="">Chọn lĩnh vực hoạt động</option>
                                                            <option value="1">Sản phẩm</option>
                                                            <option value="2">Chiến dịch</option>
                                                        </select>
                                                        <label htmlFor="form-reg-business-field">Chọn lĩnh vực hoạt động</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h3 className="form-reg-title rounded-1 bg-primary-dark fw-medium px-3 py-2 ff-base mb-3">Câu chuyện thương hiệu</h3>
                                            {products.map((product, index) => (
                                                <div key={index}>
                                                    <h4 className="fs-17px ff-secondary fw-normal mb-3">Sản phẩm đăng ký {index == 0 ? '' : index + 1}</h4>
                                                    <div className="row g-2 mb-4">
                                                        <div className="col-sm d-flex flex-column gap-3">
                                                            <div className="d-flex flex-column gap-2">
                                                                <div className="form-floating form-floating-sm">
                                                                    <input className="form-control" type="text" aria-label="" id={`productName-${index}`} placeholder="Tên sản phẩm" value={product.name} onChange={(e) => handleProductChange(index, 'name', e.target.value)} />
                                                                    <label htmlFor={`productName-${index}`}>
                                                                        Tên sản phẩm
                                                                        <span className="text-danger ff-base">*</span>
                                                                    </label>
                                                                </div>
                                                                {productErrors[index]?.name && <div className="text-danger mt-1">{productErrors[index]?.name}</div>}
                                                                <div className="form-floating form-floating-sm">
                                                                    <input className="form-control" type="text" aria-label="" id={`productDescription-${index}`} placeholder="Mô tả sản phẩm (giới thiệu ngắn gọn: chất liệu, công dụng,...)" value={product.description} onChange={(e) => handleProductChange(index, 'description', e.target.value)} />
                                                                    <label htmlFor={`productDescription-${index}`}>
                                                                        Mô tả sản phẩm (giới thiệu ngắn gọn: chất liệu, công dụng,...)
                                                                    </label>
                                                                </div>
                                                                <div className="form-floating form-floating-sm">
                                                                    <input className="form-control" type="text" aria-label="" id={`productLink-${index}`} placeholder="Link mua hàng (nếu có: Shopee, Lazada, Website...)" value={product.link} onChange={(e) => handleProductChange(index, 'link', e.target.value)} />
                                                                    <label htmlFor={`productLink-${index}`}>
                                                                        Link mua hàng (nếu có: Shopee, Lazada, Website...)
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <p className="fs-sm">Sản phẩm có phải hàng thiết kế độc quyền?</p>
                                                                <div className="flex-center-y gap-4">
                                                                    <div className="form-check form-check-lg">
                                                                        <input className="form-check-input" id={`form-reg-rdo-1-1-${index}`} type="radio" name={`form-reg-rdo-1-${index}`} checked={product.monopoly === 1} onChange={() => handleProductChange(index, 'monopoly', 1)} />
                                                                        <label className="form-check-label" htmlFor={`form-reg-rdo-1-1-${index}`}>Có</label>
                                                                    </div>
                                                                    <div className="form-check form-check-lg">
                                                                        <input className="form-check-input" id={`form-reg-rdo-1-2-${index}`} type="radio" name={`form-reg-rdo-1-${index}`} checked={product.monopoly === 0} onChange={() => handleProductChange(index, 'monopoly', 0)} />
                                                                        <label className="form-check-label" htmlFor={`form-reg-rdo-1-2-${index}`}>Không</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <p className="fs-sm">Đại diện nhãn hàng có sẵn lòng xuất hiện trong nội dung phỏng vấn/bài viết vinh danh?</p>
                                                                <div className="flex-center-y gap-4">
                                                                    <div className="form-check form-check-lg">
                                                                        <input className="form-check-input" id={`form-reg-rdo-2-1-${index}`} type="radio" name={`form-reg-rdo-2-${index}`} checked={product.interview === 1} onChange={() => handleProductChange(index, 'interview', 1)} />
                                                                        <label className="form-check-label" htmlFor={`form-reg-rdo-2-1-${index}`}>Có</label>
                                                                    </div>
                                                                    <div className="form-check form-check-lg">
                                                                        <input className="form-check-input" id={`form-reg-rdo-2-2-${index}`} type="radio" name={`form-reg-rdo-2-${index}`} checked={product.interview === 0} onChange={() => handleProductChange(index, 'interview', 0)} />
                                                                        <label className="form-check-label" htmlFor={`form-reg-rdo-2-2-${index}`}>Không</label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-auto order-first order-sm-last">
                                                            <div className="upload-image">
                                                                <label className="upload-image-body rounded-2 bg-white flex-center d-flex flex-column gap-2 p-3 flex-shrink-0">
                                                                    <input className="d-none" type="file" accept="image/*" onChange={(e) => handleImageChange(index, e)} />
                                                                    <img
                                                                        className="icon-img icon-img-image display-5"
                                                                        src={product.previewUrl && product.previewUrl !== '' ? product.previewUrl :'/images/icons/image.svg'}
                                                                        alt="Ảnh sản phẩm"
                                                                    />
                                                                    <span className="fs-sm ff-secondary opacity-50 text-body">Ảnh sản phẩm</span>
                                                                </label>
                                                                <p className="fs-11px ff-secondary mb-0 opacity-75 lh-heading">Upload tối thiểu 1 ảnh, độ phân giải tối thiểu 1500x2000px .webp/.webp</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                            <button className="btn btn-null border-dash border-white text-white justify-content-start fs-sm w-100" type="button" onClick={handleAddProduct}>
                                                <svg className="iconsvg-plus fs-base">
                                                    <use xlinkHref={"/images/sprite.svg#plus"}/>
                                                </svg>
                                                Thêm sản phẩm
                                            </button>
                                        </div>
                                        <div>
                                            <h3 className="form-reg-title rounded-1 bg-primary-dark fw-medium px-3 py-2 ff-base mb-3">Lan tỏa cùng Toplist Fandomyeunuoc</h3>
                                            <div className="d-flex flex-column gap-3">
                                                <div>
                                                    <p className="fs-sm">Tick chọn nếu Doanh nghiệp có quà tặng/ưu đãi dành cho cộng đồng Fandomyeunuoc</p>
                                                    <div className="flex-center-y gap-4"></div>
                                                    <div className="row gy-2">
                                                        <div className="col-sm-auto">
                                                            <div className="form-check form-check-lg flex-center-y">
                                                                <input className="form-check-input" id="form-reg-rdo-3-1" type="radio" name="form-reg-rdo-3" />
                                                                <label className="form-check-label flex-grow-1" htmlFor="form-reg-rdo-3-1">
                                                                    <span className="flex-center-y gap-2">Tài trợ Voucher
                                                                        <input className="form-control w-95px ms-auto" type="text" aria-label="" placeholder="Số lượng" />
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-auto">
                                                            <div className="form-check form-check-lg flex-center-y">
                                                                <input className="form-check-input" id="form-reg-rdo-3-2" type="radio" name="form-reg-rdo-3" />
                                                                <label className="form-check-label flex-grow-1" htmlFor="form-reg-rdo-3-2">
                                                                    <span className="flex-center-y gap-2">Tài trợ Merchandise
                                                                        <input className="form-control w-95px ms-auto" type="text" aria-label="" placeholder="Số lượng" />
                                                                    </span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p className="fs-sm mb-0">Quà tặng/ưu đãi sẽ được đăng tải trên website và được tặng cho cộng đồng Fandomyeunuoc thông qua hình thức minigame “tên game”</p>
                                            </div>
                                        </div>
                                        <div className="form-check text-secondary flex-center-y">
                                            <input className="form-check-input" id="form-check" type="checkbox" />
                                            <label className="form-check-label" htmlFor="form-check">Tôi đồng ý với các điều khoản</label>
                                        </div>
                                        <button className="btn btn-gold fs-lg w-100 btn-3d rounded-3 text-primary fw-bold text-uppercase" type="submit" disabled={isSubmitting}>
                                            <svg className="iconsvg-send icon-lg">
                                                <use xlinkHref={"/images/sprite.svg#send"}/>
                                            </svg>
                                            {isSubmitting ? 'Đang gửi...' : 'Gửi giới thiệu'}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default Footer;
