'use client';

import { useEffect, useState } from 'react';
import { API_ENDPOINTS } from '../utils/api-endpoints';
import commonFunc from '../utils/common';
interface FeaturedPostItem {
    title: string;
    description: string;
    link: string;
    image: string;
    brand_name: string | null;
    brand_logo: string | null;
}

const Post = () => {
    const [items, setItems] = useState<FeaturedPostItem[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchFeaturedPosts = async () => {
            setIsLoading(true);
            setError(null);
            try {
                const headers = commonFunc.generateApiHeaders();
                const res = await fetch(API_ENDPOINTS.postFeatured, { headers });
                const data = await res.json();
                if (data?.status) {
                    const list: FeaturedPostItem[] = data?.data || [];
                    setItems(list.slice(0, 6));
                } else {
                    setError(data?.message || 'Không thể tải tin tức.');
                }
            } catch (err) {
                setError('Lỗi kết nối khi tải tin tức.');
            } finally {
                setIsLoading(false);
            }
        };
        fetchFeaturedPosts();
    }, []);

    return (
        <section className="section-news section">
            <div className="container">
                <h2 className="section-title text-uppercase fw-medium mb-4 mb-md-5">
                    <span className="text-gradient-primary lh-base d-inline-block">Tin tức</span>
                </h2>

                {isLoading && (
                    <div className="flex justify-center items-center h-48">
                        <div className="loader" />
                        <p className="ml-3 opacity-75">Đang tải...</p>
                    </div>
                )}
                {error && (
                    <div className="alert alert-danger">{error}</div>
                )}

                {!isLoading && !error && (
                    <div className="row g-2 g-sm-4 g-xxl-30px">
                        {items.map((item, i) => {
                            const title = item.title || 'Bài viết';
                            const image = item.image || `/images/ex/home/<USER>/img-${(i % 6) + 1}.webp`;
                            const link = item.link || '#';
                            return (
                                <div className="col-md-4 col-6" key={`${i}-${title}`}>
                                    <div className="card card-news h-100">
                                        <a className="thumbnail thumbnail-hover" href={link} target="_blank" rel="noopener noreferrer">
                                            <span className="thumbnail-inner">
                                                <img className="thumbnail-img" src={image} alt={title} />
                                            </span>
                                        </a>
                                        <div className="card-body">
                                            <div className="flex-center-y gap-2">
                                                <span className="avatar">
                                                    <img className="img-fluid w-100 avatar-img" src={item.brand_logo || 'images/ex/home/<USER>/logo.webp'} alt="" />
                                                </span>
                                                {item.brand_name || 'Brand'}
                                            </div>
                                            <h3 className="card-title ff-secondary fw-normal">
                                                <a href={link} target="_blank" rel="noopener noreferrer">{title}</a>
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}
            </div>
        </section>
    );
};

export default Post;
